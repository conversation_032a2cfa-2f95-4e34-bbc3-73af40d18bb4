#!/usr/bin/env python3
"""
配置管理模块测试脚本
"""

import sys
import os
from pathlib import Path
from config_manager import ConfigManager
from rich.console import Console

console = Console()

def test_config_manager():
    """测试配置管理器的所有功能"""
    
    # 使用实际的cpp_kits目录
    cpp_kits_dir = "BYD_HKH_R_2.01.07.2025.07.08.4_x86"
    
    console.print("[bold blue]开始测试配置管理模块[/bold blue]")
    console.print(f"使用cpp_kits目录: {cpp_kits_dir}")
    
    try:
        cm = ConfigManager(cpp_kits_dir)
        
        # 1. 测试配置加载
        console.print("\n[bold]1. 测试配置加载[/bold]")
        configs = cm.load_configs()
        console.print(f"加载的配置: {list(configs.keys())}")
        
        # 2. 测试配置验证
        console.print("\n[bold]2. 测试配置验证[/bold]")
        
        if configs.get('ip_port'):
            is_valid, msg = cm.validate_ip_port_config(configs['ip_port'])
            console.print(f"ip_port.json 验证: {'✅' if is_valid else '❌'} {msg}")
        
        if configs.get('calidata'):
            is_valid, msg = cm.validate_calidata_config(configs['calidata'])
            console.print(f"calidata.json 验证: {'✅' if is_valid else '❌'} {msg}")
        
        # 3. 测试配置显示
        console.print("\n[bold]3. 测试配置显示[/bold]")
        cm.display_configs()
        
        # 4. 测试用户确认工作流程
        console.print("\n[bold]4. 测试用户确认工作流程[/bold]")
        console.print("[yellow]注意: 这将启动交互式确认流程，您可以测试超时或手动确认[/yellow]")
        
        # 询问是否要测试交互式流程
        test_interactive = input("\n是否测试交互式确认流程? (y/n, 默认n): ").strip().lower()
        
        if test_interactive in ['y', 'yes']:
            success = cm.run_config_workflow(timeout=5)
            if success:
                console.print("[green]✅ 配置工作流程测试成功[/green]")
            else:
                console.print("[red]❌ 配置工作流程测试失败[/red]")
        else:
            console.print("[blue]跳过交互式测试[/blue]")
        
        console.print("\n[bold green]配置管理模块测试完成[/bold green]")
        return True
        
    except Exception as e:
        console.print(f"\n[bold red]测试过程中发生错误: {e}[/bold red]")
        return False

def test_config_validation():
    """测试配置验证功能"""
    console.print("\n[bold blue]测试配置验证功能[/bold blue]")
    
    cm = ConfigManager("BYD_HKH_R_2.01.07.2025.07.08.4_x86")
    
    # 测试有效的ip_port配置
    valid_ip_port = {"ip": "***********", "port": 8080}
    is_valid, msg = cm.validate_ip_port_config(valid_ip_port)
    console.print(f"有效ip_port配置: {'✅' if is_valid else '❌'} {msg}")
    
    # 测试无效的ip_port配置
    invalid_ip_port = {"ip": "999.999.999.999", "port": 70000}
    is_valid, msg = cm.validate_ip_port_config(invalid_ip_port)
    console.print(f"无效ip_port配置: {'✅' if is_valid else '❌'} {msg}")
    
    # 测试有效的calidata配置
    valid_calidata = {
        "head_yaw": 28.45,
        "head_pitch": 26.88,
        "head_roll": 2.68,
        "left_eye_yaw": 6.19,
        "left_eye_pitch": 8.57,
        "right_eye_yaw": 13.67,
        "right_eye_pitch": 6.75,
        "left_eye_curve_mean": 0.003146,
        "right_eye_curve_mean": 0.002944
    }
    is_valid, msg = cm.validate_calidata_config(valid_calidata)
    console.print(f"有效calidata配置: {'✅' if is_valid else '❌'} {msg}")
    
    # 测试无效的calidata配置
    invalid_calidata = {
        "head_yaw": 200,  # 超出范围
        "head_pitch": 26.88,
        "head_roll": 2.68,
        "left_eye_yaw": 6.19,
        "left_eye_pitch": 8.57,
        "right_eye_yaw": 13.67,
        "right_eye_pitch": 6.75,
        "left_eye_curve_mean": 1.5,  # 超出范围
        "right_eye_curve_mean": 0.002944
    }
    is_valid, msg = cm.validate_calidata_config(invalid_calidata)
    console.print(f"无效calidata配置: {'✅' if is_valid else '❌'} {msg}")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        console.print("""
配置管理模块测试脚本

用法:
    python test_config_manager.py [选项]

选项:
    --help          显示帮助信息
    --validation    只测试配置验证功能
    --interactive   运行完整的交互式测试

功能:
    1. 测试配置文件加载
    2. 测试配置验证
    3. 测试配置显示
    4. 测试用户确认工作流程
    5. 测试配置修改功能

注意:
    - 确保cpp_kits目录存在
    - 交互式测试需要用户输入
    - 超时测试会在5秒后自动确认
        """)
        return
    
    if len(sys.argv) > 1 and sys.argv[1] == "--validation":
        test_config_validation()
        return
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        # 强制运行交互式测试
        cm = ConfigManager("BYD_HKH_R_2.01.07.2025.07.08.4_x86")
        success = cm.run_config_workflow(timeout=5)
        console.print(f"交互式测试结果: {'成功' if success else '失败'}")
        return
    
    # 运行基本测试
    success = test_config_manager()
    
    # 运行验证测试
    test_config_validation()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
