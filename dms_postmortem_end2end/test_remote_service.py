#!/usr/bin/env python3
"""
远程服务管理模块测试脚本
"""

import sys
import os
from pathlib import Path
from remote_service_manager import RemoteServiceManager
from rich.console import Console

console = Console()

def test_remote_service_manager():
    """测试远程服务管理器的所有功能"""
    
    # 使用实际的cpp_kits目录
    cpp_kits_dir = "BYD_HKH_R_2.01.07.2025.07.08.4_x86"
    
    console.print("[bold blue]开始测试远程服务管理模块[/bold blue]")
    console.print(f"使用cpp_kits目录: {cpp_kits_dir}")
    
    try:
        with RemoteServiceManager(cpp_kits_dir) as rsm:
            # 1. 测试配置加载
            console.print("\n[bold]1. 测试远程服务配置加载[/bold]")
            config = rsm.load_remote_config()
            console.print(f"远程服务配置加载成功")

            # 2. 显示cpp程序配置
            console.print("\n[bold]2. 显示cpp程序配置文件[/bold]")
            cpp_configs = rsm.display_cpp_configs()

            # 3. 测试SSH连接
            console.print("\n[bold]3. 测试SSH连接[/bold]")
            if not rsm.connect_ssh():
                console.print("[red]SSH连接失败，无法继续测试[/red]")
                return False
            
            # 4. 测试模型文件一致性检查
            console.print("\n[bold]4. 测试模型文件一致性检查[/bold]")
            consistency_results = rsm.check_model_consistency()
            console.print(f"一致性检查结果: {consistency_results}")

            # 5. 测试模型文件同步（如果需要）
            if not all(consistency_results.values()):
                console.print("\n[bold]5. 测试模型文件同步[/bold]")
                sync_success = rsm.sync_model_files()
                if sync_success:
                    console.print("[green]模型文件同步成功[/green]")
                else:
                    console.print("[red]模型文件同步失败[/red]")
            else:
                console.print("\n[bold]5. 模型文件已是最新，跳过同步[/bold]")

            # 6. 测试服务状态检查
            console.print("\n[bold]6. 测试服务状态检查[/bold]")
            service_running = rsm.check_service_status()

            # 7. 清理重复服务实例（如果有）
            console.print("\n[bold]7. 清理重复服务实例[/bold]")
            cleanup_success = rsm.cleanup_duplicate_services()
            if cleanup_success:
                console.print("[green]服务实例清理完成[/green]")
            else:
                console.print("[red]服务实例清理失败[/red]")

            # 8. 重新检查服务状态
            console.print("\n[bold]8. 重新检查服务状态[/bold]")
            service_running = rsm.check_service_status()

            # 9. 测试服务启动（如果服务未运行）
            if not service_running:
                console.print("\n[bold]9. 测试服务启动[/bold]")
                start_success = rsm.start_service()
                if start_success:
                    console.print("[green]服务启动成功[/green]")
                else:
                    console.print("[red]服务启动失败[/red]")
            else:
                console.print("\n[bold]9. 服务已在运行，绝对禁止重复启动[/bold]")
            
            console.print("\n[bold green]远程服务管理模块测试完成[/bold green]")
            return True
            
    except Exception as e:
        console.print(f"\n[bold red]测试过程中发生错误: {e}[/bold red]")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        console.print("""
远程服务管理模块测试脚本

用法:
    python test_remote_service.py

功能:
    1. 测试SSH连接
    2. 测试配置文件加载
    3. 测试模型文件一致性检查
    4. 测试模型文件同步
    5. 测试服务状态检查
    6. 测试服务启动

注意:
    - 确保ip_port.json配置正确
    - 确保远程服务器可访问
    - 确保有足够的权限操作远程文件和服务
        """)
        return
    
    success = test_remote_service_manager()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
