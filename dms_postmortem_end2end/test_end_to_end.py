#!/usr/bin/env python3
"""
端到端自动化流程测试脚本
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from rich.console import Console
from dms_automation_controller import DMSAutomationController

console = Console()

def test_end_to_end_workflow():
    """测试端到端自动化工作流程"""
    
    console.print("[bold blue]🧪 端到端自动化流程测试[/bold blue]")
    
    # 测试参数
    test_video = "/home/<USER>/data/dms/byd/sc3e_r/250715/2025-07-14 14-22-29_000000_000500_1920_1080_0_0.mp4"
    cpp_kits_dir = "BYD_HKH_R_2.01.07.2025.07.08.4_x86"
    time_ranges = "00:00:00-00:00:03;00:00:05-00:00:08"  # 短时间段用于测试
    roi = "1280:800:0:0"
    
    console.print(f"[cyan]测试视频:[/cyan] {test_video}")
    console.print(f"[cyan]CPP Kits:[/cyan] {cpp_kits_dir}")
    console.print(f"[cyan]时间范围:[/cyan] {time_ranges}")
    console.print(f"[cyan]ROI:[/cyan] {roi}")
    
    # 验证测试文件存在
    if not os.path.exists(test_video):
        console.print(f"[red]❌ 测试视频不存在: {test_video}[/red]")
        return False
    
    if not os.path.exists(cpp_kits_dir):
        console.print(f"[red]❌ CPP Kits目录不存在: {cpp_kits_dir}[/red]")
        return False
    
    try:
        # 创建自动化控制器
        controller = DMSAutomationController(cpp_kits_dir)
        
        # 运行端到端工作流程
        success = controller.run_end_to_end_workflow(
            test_video, 
            time_ranges, 
            roi
        )
        
        if success:
            console.print("\n[bold green]🎉 端到端测试成功！[/bold green]")
            return True
        else:
            console.print("\n[bold red]❌ 端到端测试失败[/bold red]")
            return False
            
    except Exception as e:
        console.print(f"\n[bold red]❌ 测试过程中发生错误: {e}[/bold red]")
        import traceback
        traceback.print_exc()
        return False

def test_individual_components():
    """测试各个组件的独立功能"""
    
    console.print("\n[bold blue]🔧 组件独立功能测试[/bold blue]")
    
    cpp_kits_dir = "BYD_HKH_R_2.01.07.2025.07.08.4_x86"
    
    try:
        controller = DMSAutomationController(cpp_kits_dir)
        
        # 1. 测试管理器初始化
        console.print("\n[bold]1. 测试管理器初始化[/bold]")
        if controller.initialize_managers():
            console.print("[green]✅ 管理器初始化成功[/green]")
        else:
            console.print("[red]❌ 管理器初始化失败[/red]")
            return False
        
        # 2. 测试远程服务工作流程
        console.print("\n[bold]2. 测试远程服务工作流程[/bold]")
        if controller.run_remote_service_workflow():
            console.print("[green]✅ 远程服务工作流程成功[/green]")
        else:
            console.print("[red]❌ 远程服务工作流程失败[/red]")
            return False
        
        # 3. 测试配置工作流程
        console.print("\n[bold]3. 测试配置工作流程[/bold]")
        console.print("[yellow]注意: 这将启动5秒超时的配置确认流程[/yellow]")
        
        if controller.run_config_workflow():
            console.print("[green]✅ 配置工作流程成功[/green]")
        else:
            console.print("[red]❌ 配置工作流程失败[/red]")
            return False
        
        console.print("\n[bold green]✅ 所有组件测试通过[/bold green]")
        return True
        
    except Exception as e:
        console.print(f"\n[bold red]❌ 组件测试失败: {e}[/bold red]")
        return False
    finally:
        # 清理连接
        if hasattr(controller, 'remote_manager') and controller.remote_manager:
            controller.remote_manager.close_connection()

def test_video_processing_only():
    """仅测试视频处理部分"""
    
    console.print("\n[bold blue]🎬 视频处理功能测试[/bold blue]")
    
    test_video = "/home/<USER>/data/dms/byd/sc3e_r/250715/2025-07-14 14-22-29_000000_000500_1920_1080_0_0.mp4"
    time_ranges = "00:00:00-00:00:02;00:00:03-00:00:05"
    roi = "1280:800:0:0"
    
    if not os.path.exists(test_video):
        console.print(f"[red]❌ 测试视频不存在: {test_video}[/red]")
        return False
    
    try:
        controller = DMSAutomationController("BYD_HKH_R_2.01.07.2025.07.08.4_x86")
        
        # 初始化缓存处理器
        from cached_video_processor import CachedVideoProcessor
        controller.cache_processor = CachedVideoProcessor()
        
        # 处理视频段
        video_segments = controller.process_video_segments(test_video, time_ranges, roi)
        
        if video_segments:
            console.print(f"[green]✅ 视频处理成功，生成 {len(video_segments)} 个文件[/green]")
            for i, file_path in enumerate(video_segments, 1):
                console.print(f"  {i}. {file_path}")
            return True
        else:
            console.print("[red]❌ 视频处理失败[/red]")
            return False
            
    except Exception as e:
        console.print(f"[red]❌ 视频处理测试失败: {e}[/red]")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='端到端自动化流程测试')
    parser.add_argument('--test-type', choices=['full', 'components', 'video'], 
                       default='full', help='测试类型')
    parser.add_argument('--skip-dms', action='store_true', 
                       help='跳过DMS处理部分（仅测试视频裁剪）')
    
    args = parser.parse_args()
    
    console.print("[bold cyan]DMS端到端自动化流程测试工具[/bold cyan]")
    console.print("="*60)
    
    success = False
    
    if args.test_type == 'full':
        console.print("[blue]运行完整端到端测试...[/blue]")
        success = test_end_to_end_workflow()
    elif args.test_type == 'components':
        console.print("[blue]运行组件独立测试...[/blue]")
        success = test_individual_components()
    elif args.test_type == 'video':
        console.print("[blue]运行视频处理测试...[/blue]")
        success = test_video_processing_only()
    
    if success:
        console.print(f"\n[bold green]🎉 {args.test_type} 测试成功完成！[/bold green]")
        return 0
    else:
        console.print(f"\n[bold red]❌ {args.test_type} 测试失败[/bold red]")
        return 1

if __name__ == "__main__":
    sys.exit(main())
