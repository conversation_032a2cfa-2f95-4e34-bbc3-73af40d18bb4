#!/usr/bin/env python3
"""
远程服务管理模块
负责远程服务器的连接、配置校验、模型同步和服务管理
"""

import os
import json
import hashlib
import subprocess
import time
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import paramiko
from rich.console import Console

console = Console()

class RemoteServiceManager:
    """远程服务管理器"""

    def __init__(self, cpp_kits_dir: str, remote_config_file: str = "remote_config.json"):
        """
        初始化远程服务管理器

        Args:
            cpp_kits_dir: cpp_kits目录路径
            remote_config_file: 远程服务配置文件路径
        """
        self.cpp_kits_dir = Path(cpp_kits_dir)
        self.remote_config_file = Path(remote_config_file)
        self.ip_port_file = self.cpp_kits_dir / "ip_port.json"
        self.calidata_file = self.cpp_kits_dir / "calidata.json"
        self.ssh_client = None
        self.remote_config = None
        
    def load_remote_config(self) -> Dict[str, Any]:
        """加载远程服务器配置"""
        try:
            if not self.remote_config_file.exists():
                raise FileNotFoundError(f"远程配置文件不存在: {self.remote_config_file}")

            with open(self.remote_config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 验证必要字段
            required_sections = ['ssh', 'remote_paths', 'model_files']
            for section in required_sections:
                if section not in config:
                    raise ValueError(f"配置文件缺少必要部分: {section}")

            # 验证SSH配置
            ssh_required = ['host', 'port', 'username', 'password']
            for field in ssh_required:
                if field not in config['ssh']:
                    raise ValueError(f"SSH配置缺少必要字段: {field}")

            self.remote_config = config
            return config

        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise RuntimeError(f"加载远程配置失败: {e}")
    
    def connect_ssh(self) -> bool:
        """建立SSH连接"""
        if not self.remote_config:
            self.load_remote_config()
            
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            ssh_config = self.remote_config['ssh']
            console.print(f"[blue]正在连接远程服务器 {ssh_config['host']}:{ssh_config['port']} (SSH)...[/blue]")

            # 清除代理环境变量，避免影响SSH连接
            import os
            env_backup = {}
            proxy_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY']
            for var in proxy_vars:
                if var in os.environ:
                    env_backup[var] = os.environ[var]
                    del os.environ[var]

            try:
                self.ssh_client.connect(
                    hostname=ssh_config['host'],
                    port=ssh_config['port'],
                    username=ssh_config['username'],
                    password=ssh_config['password'],
                    timeout=10
                )
            finally:
                # 恢复代理环境变量
                for var, value in env_backup.items():
                    os.environ[var] = value
            
            console.print("[green]SSH连接成功[/green]")
            return True
            
        except paramiko.AuthenticationException:
            console.print("[red]SSH认证失败，请检查用户名和密码[/red]")
            return False
        except paramiko.SSHException as e:
            console.print(f"[red]SSH连接失败: {e}[/red]")
            return False
        except Exception as e:
            console.print(f"[red]连接远程服务器失败: {e}[/red]")
            return False
    
    def execute_remote_command(self, command: str, timeout: int = 30) -> Tuple[bool, str, str]:
        """
        执行远程命令
        
        Args:
            command: 要执行的命令
            timeout: 超时时间（秒）
            
        Returns:
            (成功标志, stdout, stderr)
        """
        if not self.ssh_client:
            raise RuntimeError("SSH连接未建立")
            
        try:
            stdin, stdout, stderr = self.ssh_client.exec_command(command, timeout=timeout)
            stdout_data = stdout.read().decode('utf-8')
            stderr_data = stderr.read().decode('utf-8')
            exit_status = stdout.channel.recv_exit_status()
            
            return exit_status == 0, stdout_data, stderr_data
            
        except Exception as e:
            return False, "", str(e)
    
    def get_file_hash(self, file_path: Path) -> str:
        """计算文件MD5哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            raise RuntimeError(f"计算文件哈希失败: {e}")
    
    def get_remote_file_hash(self, remote_path: str) -> Optional[str]:
        """获取远程文件MD5哈希值"""
        success, stdout, stderr = self.execute_remote_command(f"md5sum {remote_path}")
        if success and stdout:
            return stdout.split()[0]
        return None
    
    def check_model_consistency(self) -> Dict[str, bool]:
        """检查本地和远程模型文件一致性"""
        console.print("[blue]检查模型文件一致性...[/blue]")

        results = {}
        model_files = self.remote_config['model_files']
        remote_model_dir = self.remote_config['remote_paths']['model_dir']

        for model_file in model_files:
            local_file = self.cpp_kits_dir / model_file
            remote_path = f"{remote_model_dir}/{model_file}"
            
            if not local_file.exists():
                console.print(f"[yellow]警告: 本地文件不存在 {local_file}[/yellow]")
                results[model_file] = False
                continue
                
            # 获取本地文件哈希
            try:
                local_hash = self.get_file_hash(local_file)
            except Exception as e:
                console.print(f"[red]获取本地文件哈希失败 {model_file}: {e}[/red]")
                results[model_file] = False
                continue
                
            # 获取远程文件哈希
            remote_hash = self.get_remote_file_hash(remote_path)
            
            if remote_hash is None:
                console.print(f"[yellow]远程文件不存在或无法访问: {remote_path}[/yellow]")
                results[model_file] = False
            elif local_hash == remote_hash:
                console.print(f"[green]✓ {model_file} 文件一致[/green]")
                results[model_file] = True
            else:
                console.print(f"[red]✗ {model_file} 文件不一致[/red]")
                console.print(f"  本地哈希: {local_hash}")
                console.print(f"  远程哈希: {remote_hash}")
                results[model_file] = False
                
        return results
    
    def sync_model_files(self, force_sync: bool = False) -> bool:
        """同步模型文件到远程服务器"""
        console.print("[blue]开始同步模型文件...[/blue]")

        if not force_sync:
            # 先检查一致性
            consistency_results = self.check_model_consistency()
            if all(consistency_results.values()):
                console.print("[green]所有模型文件已是最新，无需同步[/green]")
                return True

        try:
            model_files = self.remote_config['model_files']
            remote_model_dir = self.remote_config['remote_paths']['model_dir']
            ssh_config = self.remote_config['ssh']

            # 确保远程目录存在
            console.print(f"[blue]确保远程目录 {remote_model_dir} 存在[/blue]")
            success, stdout, stderr = self.execute_remote_command(f"mkdir -p {remote_model_dir}")
            if not success:
                console.print(f"[red]创建远程目录失败: {stderr}[/red]")
                return False

            # 使用scp命令同步文件
            import subprocess
            import os

            # 清除代理环境变量
            env = os.environ.copy()
            proxy_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY']
            for var in proxy_vars:
                env.pop(var, None)

            # 同步每个模型文件
            for model_file in model_files:
                local_file = self.cpp_kits_dir / model_file
                remote_path = f"{ssh_config['username']}@{ssh_config['host']}:{remote_model_dir}/{model_file}"

                if not local_file.exists():
                    console.print(f"[yellow]跳过不存在的本地文件: {local_file}[/yellow]")
                    continue

                console.print(f"[blue]上传 {model_file}...[/blue]")
                try:
                    # 使用sshpass + scp命令上传文件
                    scp_cmd = [
                        'sshpass', '-p', ssh_config['password'],
                        'scp', '-o', 'StrictHostKeyChecking=no',
                        '-o', f'Port={ssh_config["port"]}',
                        str(local_file), remote_path
                    ]

                    result = subprocess.run(
                        scp_cmd,
                        capture_output=True,
                        text=True,
                        env=env,
                        timeout=60
                    )

                    if result.returncode == 0:
                        console.print(f"[green]✓ {model_file} 上传完成[/green]")
                    else:
                        console.print(f"[red]上传 {model_file} 失败: {result.stderr}[/red]")
                        return False

                except Exception as e:
                    console.print(f"[red]上传 {model_file} 失败: {e}[/red]")
                    return False

            # 同步配置文件
            config_files = self.remote_config.get('config_files', [])
            for config_file in config_files:
                local_config_file = self.cpp_kits_dir / config_file
                if local_config_file.exists():
                    remote_config_path = f"{ssh_config['username']}@{ssh_config['host']}:{remote_model_dir}/{config_file}"
                    console.print(f"[blue]上传 {config_file}...[/blue]")
                    try:
                        scp_cmd = [
                            'sshpass', '-p', ssh_config['password'],
                            'scp', '-o', 'StrictHostKeyChecking=no',
                            '-o', f'Port={ssh_config["port"]}',
                            str(local_config_file), remote_config_path
                        ]

                        result = subprocess.run(
                            scp_cmd,
                            capture_output=True,
                            text=True,
                            env=env,
                            timeout=60
                        )

                        if result.returncode == 0:
                            console.print(f"[green]✓ {config_file} 上传完成[/green]")
                        else:
                            console.print(f"[red]上传 {config_file} 失败: {result.stderr}[/red]")
                            return False

                    except Exception as e:
                        console.print(f"[red]上传 {config_file} 失败: {e}[/red]")
                        return False

            console.print("[green]模型文件同步完成[/green]")
            return True

        except Exception as e:
            console.print(f"[red]模型文件同步失败: {e}[/red]")
            return False
    
    def check_service_status(self, service_path: str = None) -> bool:
        """检查远程服务状态"""
        if service_path is None:
            service_path = self.remote_config['remote_paths']['service_executable']

        console.print(f"[blue]检查服务状态: {service_path}[/blue]")

        # 检查进程是否运行 - 使用ps命令替代pgrep
        service_name = service_path.split('/')[-1]  # 获取服务名称
        success, stdout, stderr = self.execute_remote_command(f"ps | grep {service_name} | grep -v grep")

        if success and stdout.strip():
            lines = stdout.strip().split('\n')
            if len(lines) > 1:
                console.print(f"[red]⚠️  发现多个服务实例运行 ({len(lines)}个)，这违反了单实例原则！[/red]")
                for line in lines:
                    console.print(f"  {line}")
                return True  # 有服务在运行，但状态异常
            else:
                console.print(f"[green]服务正在运行: {stdout.strip()}[/green]")
                return True
        else:
            console.print("[yellow]服务未运行[/yellow]")
            return False

    def cleanup_duplicate_services(self, service_path: str = None) -> bool:
        """清理重复的服务实例，只保留一个"""
        if service_path is None:
            service_path = self.remote_config['remote_paths']['service_executable']

        service_name = service_path.split('/')[-1]
        console.print(f"[blue]清理重复的服务实例: {service_name}[/blue]")

        # 获取所有服务进程
        success, stdout, stderr = self.execute_remote_command(f"ps | grep {service_name} | grep -v grep")

        if not success or not stdout.strip():
            console.print("[green]没有发现运行的服务实例[/green]")
            return True

        lines = stdout.strip().split('\n')
        if len(lines) <= 1:
            console.print("[green]只有一个服务实例在运行，无需清理[/green]")
            return True

        console.print(f"[yellow]发现 {len(lines)} 个服务实例，将清理多余实例[/yellow]")

        # 提取PID并杀死多余进程（保留第一个）
        pids = []
        for line in lines:
            parts = line.strip().split()
            if len(parts) >= 1:
                try:
                    pid = int(parts[0])
                    pids.append(pid)
                except ValueError:
                    continue

        if len(pids) <= 1:
            console.print("[green]只有一个有效PID，无需清理[/green]")
            return True

        # 保留第一个PID，杀死其他的
        keep_pid = pids[0]
        kill_pids = pids[1:]

        console.print(f"[blue]保留PID {keep_pid}，清理PID: {kill_pids}[/blue]")

        for pid in kill_pids:
            success, stdout, stderr = self.execute_remote_command(f"kill {pid}")
            if success:
                console.print(f"[green]✓ 已清理PID {pid}[/green]")
            else:
                console.print(f"[red]✗ 清理PID {pid} 失败: {stderr}[/red]")

        # 等待进程清理完成
        time.sleep(2)

        # 验证清理结果
        success, stdout, stderr = self.execute_remote_command(f"ps | grep {service_name} | grep -v grep")
        if success and stdout.strip():
            remaining_lines = stdout.strip().split('\n')
            if len(remaining_lines) == 1:
                console.print(f"[green]✓ 清理完成，现在只有一个服务实例在运行[/green]")
                return True
            else:
                console.print(f"[red]✗ 清理未完全成功，仍有 {len(remaining_lines)} 个实例[/red]")
                return False
        else:
            console.print("[yellow]清理后没有服务实例在运行[/yellow]")
            return True

    def start_service(self, service_path: str = None) -> bool:
        """启动远程服务 - 绝对禁止重复启动已运行的服务"""
        if service_path is None:
            service_path = self.remote_config['remote_paths']['service_executable']

        # 首先检查服务是否已经运行
        if self.check_service_status(service_path):
            console.print("[yellow]⚠️  服务已在运行，绝对禁止重复启动！[/yellow]")
            return True

        console.print(f"[blue]启动服务: {service_path}[/blue]")

        # 检查服务文件是否存在
        success, stdout, stderr = self.execute_remote_command(f"test -f {service_path}")
        if not success:
            console.print(f"[red]服务文件不存在: {service_path}[/red]")
            return False

        # 获取服务目录
        service_dir = self.remote_config['remote_paths']['userfs_dir']

        # 启动服务（后台运行）
        start_command = f"cd {service_dir} && {service_path} > service.log 2>&1 &"
        success, stdout, stderr = self.execute_remote_command(start_command)

        if not success:
            console.print(f"[red]启动服务失败: {stderr}[/red]")
            return False

        # 等待服务启动
        time.sleep(3)

        # 验证服务是否成功启动
        if self.check_service_status(service_path):
            console.print("[green]服务启动成功[/green]")
            return True
        else:
            console.print("[red]服务启动失败[/red]")
            return False
    
    def display_cpp_configs(self) -> Dict[str, Any]:
        """显示cpp程序的配置文件内容"""
        configs = {}

        console.print("\n[bold blue]CPP程序配置文件内容：[/bold blue]")

        # 显示ip_port.json
        if self.ip_port_file.exists():
            try:
                with open(self.ip_port_file, 'r', encoding='utf-8') as f:
                    ip_port_config = json.load(f)
                configs['ip_port'] = ip_port_config
                console.print(f"\n[bold]ip_port.json:[/bold]")
                console.print(json.dumps(ip_port_config, indent=2, ensure_ascii=False))
            except Exception as e:
                console.print(f"[red]读取ip_port.json失败: {e}[/red]")
        else:
            console.print(f"[yellow]ip_port.json不存在: {self.ip_port_file}[/yellow]")

        # 显示calidata.json
        if self.calidata_file.exists():
            try:
                with open(self.calidata_file, 'r', encoding='utf-8') as f:
                    calidata_config = json.load(f)
                configs['calidata'] = calidata_config
                console.print(f"\n[bold]calidata.json:[/bold]")
                console.print(json.dumps(calidata_config, indent=2, ensure_ascii=False))
            except Exception as e:
                console.print(f"[red]读取calidata.json失败: {e}[/red]")
        else:
            console.print(f"[yellow]calidata.json不存在: {self.calidata_file}[/yellow]")

        return configs

    def close_connection(self):
        """关闭SSH连接"""
        if self.ssh_client:
            self.ssh_client.close()
            self.ssh_client = None
            console.print("[blue]SSH连接已关闭[/blue]")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_connection()
