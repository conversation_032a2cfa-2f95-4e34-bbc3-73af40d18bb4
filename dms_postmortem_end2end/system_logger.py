#!/usr/bin/env python3
"""
系统日志记录模块
提供完整的日志记录、错误追踪和性能监控功能
"""

import os
import sys
import time
import json
import logging
import traceback
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.logging import RichHandler

console = Console()

class SystemLogger:
    """系统日志记录器"""
    
    def __init__(self, log_dir: str = "logs", log_level: str = "INFO"):
        """
        初始化系统日志记录器
        
        Args:
            log_dir: 日志目录
            log_level: 日志级别
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 创建日志文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.log_dir / f"dms_automation_{timestamp}.log"
        self.error_log_file = self.log_dir / f"dms_errors_{timestamp}.log"
        self.performance_log_file = self.log_dir / f"dms_performance_{timestamp}.log"
        
        # 设置日志级别
        self.log_level = getattr(logging, log_level.upper())
        
        # 初始化日志记录器
        self._setup_loggers()
        
        # 性能监控数据
        self.performance_data = {
            'start_time': None,
            'end_time': None,
            'operations': [],
            'errors': [],
            'cache_stats': {},
            'memory_usage': []
        }
        
        console.print(f"[blue]📝 系统日志记录器初始化完成[/blue]")
        console.print(f"[cyan]日志目录:[/cyan] {self.log_dir}")
        console.print(f"[cyan]主日志文件:[/cyan] {self.log_file}")
    
    def _setup_loggers(self):
        """设置日志记录器"""
        # 主日志记录器
        self.main_logger = logging.getLogger('dms_main')
        self.main_logger.setLevel(self.log_level)
        
        # 错误日志记录器
        self.error_logger = logging.getLogger('dms_error')
        self.error_logger.setLevel(logging.ERROR)
        
        # 性能日志记录器
        self.performance_logger = logging.getLogger('dms_performance')
        self.performance_logger.setLevel(logging.INFO)
        
        # 清除现有处理器
        for logger in [self.main_logger, self.error_logger, self.performance_logger]:
            logger.handlers.clear()
        
        # 创建格式化器
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 主日志文件处理器
        main_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        main_handler.setFormatter(file_formatter)
        self.main_logger.addHandler(main_handler)
        
        # 错误日志文件处理器
        error_handler = logging.FileHandler(self.error_log_file, encoding='utf-8')
        error_handler.setFormatter(file_formatter)
        self.error_logger.addHandler(error_handler)
        
        # 性能日志文件处理器
        perf_handler = logging.FileHandler(self.performance_log_file, encoding='utf-8')
        perf_handler.setFormatter(file_formatter)
        self.performance_logger.addHandler(perf_handler)
        
        # 控制台处理器（使用Rich）
        console_handler = RichHandler(console=console, show_path=False)
        console_handler.setLevel(logging.WARNING)  # 只在控制台显示警告和错误
        self.main_logger.addHandler(console_handler)
    
    def log_info(self, message: str, component: str = "SYSTEM"):
        """记录信息日志"""
        self.main_logger.info(f"[{component}] {message}")
    
    def log_warning(self, message: str, component: str = "SYSTEM"):
        """记录警告日志"""
        self.main_logger.warning(f"[{component}] {message}")
    
    def log_error(self, message: str, component: str = "SYSTEM", exception: Exception = None):
        """记录错误日志"""
        error_msg = f"[{component}] {message}"
        
        if exception:
            error_msg += f" - Exception: {str(exception)}"
            # 记录完整的异常堆栈
            self.error_logger.error(f"{error_msg}\n{traceback.format_exc()}")
        else:
            self.error_logger.error(error_msg)
        
        self.main_logger.error(error_msg)
        
        # 添加到性能数据
        self.performance_data['errors'].append({
            'timestamp': datetime.now().isoformat(),
            'component': component,
            'message': message,
            'exception': str(exception) if exception else None
        })
    
    def log_operation_start(self, operation: str, component: str = "SYSTEM", **kwargs):
        """记录操作开始"""
        operation_data = {
            'operation': operation,
            'component': component,
            'start_time': time.time(),
            'start_timestamp': datetime.now().isoformat(),
            'parameters': kwargs,
            'end_time': None,
            'duration': None,
            'status': 'STARTED'
        }
        
        self.performance_data['operations'].append(operation_data)
        self.log_info(f"开始操作: {operation}", component)
        
        return len(self.performance_data['operations']) - 1  # 返回操作索引
    
    def log_operation_end(self, operation_index: int, status: str = "SUCCESS", **kwargs):
        """记录操作结束"""
        if 0 <= operation_index < len(self.performance_data['operations']):
            operation = self.performance_data['operations'][operation_index]
            operation['end_time'] = time.time()
            operation['duration'] = operation['end_time'] - operation['start_time']
            operation['status'] = status
            operation['result'] = kwargs
            
            self.performance_logger.info(
                f"操作完成: {operation['operation']} - "
                f"耗时: {operation['duration']:.2f}s - "
                f"状态: {status}"
            )
            
            self.log_info(
                f"完成操作: {operation['operation']} "
                f"(耗时: {operation['duration']:.2f}s, 状态: {status})",
                operation['component']
            )
    
    def log_cache_stats(self, stats: Dict[str, Any]):
        """记录缓存统计信息"""
        self.performance_data['cache_stats'] = {
            'timestamp': datetime.now().isoformat(),
            **stats
        }
        
        self.performance_logger.info(f"缓存统计: {json.dumps(stats, ensure_ascii=False)}")
    
    def log_memory_usage(self):
        """记录内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            memory_data = {
                'timestamp': datetime.now().isoformat(),
                'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
                'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
                'percent': process.memory_percent()
            }
            
            self.performance_data['memory_usage'].append(memory_data)
            self.performance_logger.info(f"内存使用: {json.dumps(memory_data, ensure_ascii=False)}")
            
        except ImportError:
            self.log_warning("psutil未安装，无法记录内存使用情况")
        except Exception as e:
            self.log_error("记录内存使用失败", exception=e)
    
    def start_session(self):
        """开始会话记录"""
        self.performance_data['start_time'] = time.time()
        self.log_info("=== DMS自动化会话开始 ===")
        self.log_memory_usage()
    
    def end_session(self):
        """结束会话记录"""
        self.performance_data['end_time'] = time.time()
        total_duration = self.performance_data['end_time'] - self.performance_data['start_time']
        
        self.log_info(f"=== DMS自动化会话结束 (总耗时: {total_duration:.2f}s) ===")
        self.log_memory_usage()
        
        # 生成会话总结
        self._generate_session_summary()
    
    def _generate_session_summary(self):
        """生成会话总结"""
        summary = {
            'session_duration': self.performance_data['end_time'] - self.performance_data['start_time'],
            'total_operations': len(self.performance_data['operations']),
            'successful_operations': len([op for op in self.performance_data['operations'] if op['status'] == 'SUCCESS']),
            'failed_operations': len([op for op in self.performance_data['operations'] if op['status'] != 'SUCCESS']),
            'total_errors': len(self.performance_data['errors']),
            'cache_stats': self.performance_data['cache_stats'],
            'memory_peak': max([m['rss_mb'] for m in self.performance_data['memory_usage']], default=0)
        }
        
        # 保存详细的性能数据
        performance_file = self.log_dir / f"session_performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(self.performance_data, f, indent=2, ensure_ascii=False)
        
        # 记录总结
        self.performance_logger.info(f"会话总结: {json.dumps(summary, ensure_ascii=False)}")
        
        console.print(f"\n[bold blue]📊 会话总结[/bold blue]")
        console.print(f"[cyan]总耗时:[/cyan] {summary['session_duration']:.2f} 秒")
        console.print(f"[cyan]总操作数:[/cyan] {summary['total_operations']}")
        console.print(f"[cyan]成功操作:[/cyan] {summary['successful_operations']}")
        console.print(f"[cyan]失败操作:[/cyan] {summary['failed_operations']}")
        console.print(f"[cyan]错误总数:[/cyan] {summary['total_errors']}")
        console.print(f"[cyan]内存峰值:[/cyan] {summary['memory_peak']:.1f} MB")
        console.print(f"[cyan]详细数据:[/cyan] {performance_file}")
    
    def get_log_files(self) -> Dict[str, str]:
        """获取所有日志文件路径"""
        return {
            'main_log': str(self.log_file),
            'error_log': str(self.error_log_file),
            'performance_log': str(self.performance_log_file)
        }
    
    def cleanup_old_logs(self, days: int = 7):
        """清理旧日志文件"""
        try:
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            cleaned_count = 0
            
            for log_file in self.log_dir.glob("*.log"):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    cleaned_count += 1
            
            for json_file in self.log_dir.glob("session_performance_*.json"):
                if json_file.stat().st_mtime < cutoff_time:
                    json_file.unlink()
                    cleaned_count += 1
            
            if cleaned_count > 0:
                self.log_info(f"清理了 {cleaned_count} 个旧日志文件")
            
        except Exception as e:
            self.log_error("清理旧日志文件失败", exception=e)

# 全局日志记录器实例
_global_logger = None

def get_logger() -> SystemLogger:
    """获取全局日志记录器实例"""
    global _global_logger
    if _global_logger is None:
        _global_logger = SystemLogger()
    return _global_logger

def init_logger(log_dir: str = "logs", log_level: str = "INFO") -> SystemLogger:
    """初始化全局日志记录器"""
    global _global_logger
    _global_logger = SystemLogger(log_dir, log_level)
    return _global_logger
