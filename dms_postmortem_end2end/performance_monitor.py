#!/usr/bin/env python3
"""
性能监控和优化模块
提供性能监控、基准测试和优化建议功能
"""

import os
import time
import psutil
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn
from system_logger import get_logger

console = Console()

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    disk_io_read: int
    disk_io_write: int
    network_sent: int
    network_recv: int

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, monitoring_interval: float = 1.0):
        """
        初始化性能监控器
        
        Args:
            monitoring_interval: 监控间隔（秒）
        """
        self.monitoring_interval = monitoring_interval
        self.logger = get_logger()
        self.is_monitoring = False
        self.monitor_thread = None
        self.metrics_history: List[PerformanceMetrics] = []
        self.process = psutil.Process()
        
        # 基准性能数据
        self.baseline_metrics = None
        self.performance_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_io_rate': 100 * 1024 * 1024,  # 100MB/s
        }
        
        console.print("[blue]📊 性能监控器初始化完成[/blue]")
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """获取当前性能指标"""
        try:
            # CPU使用率
            cpu_percent = self.process.cpu_percent()
            
            # 内存使用情况
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            memory_percent = self.process.memory_percent()
            
            # 磁盘IO
            io_counters = self.process.io_counters()
            disk_io_read = io_counters.read_bytes
            disk_io_write = io_counters.write_bytes
            
            # 网络IO（系统级别）
            net_io = psutil.net_io_counters()
            network_sent = net_io.bytes_sent
            network_recv = net_io.bytes_recv
            
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_mb=memory_mb,
                memory_percent=memory_percent,
                disk_io_read=disk_io_read,
                disk_io_write=disk_io_write,
                network_sent=network_sent,
                network_recv=network_recv
            )
            
        except Exception as e:
            self.logger.log_error("获取性能指标失败", "PERFORMANCE", e)
            return None
    
    def start_monitoring(self):
        """开始性能监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        console.print("[green]📈 性能监控已启动[/green]")
        self.logger.log_info("性能监控已启动", "PERFORMANCE")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        
        console.print("[yellow]📉 性能监控已停止[/yellow]")
        self.logger.log_info("性能监控已停止", "PERFORMANCE")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = self.get_current_metrics()
                if metrics:
                    self.metrics_history.append(metrics)
                    
                    # 检查性能阈值
                    self._check_performance_thresholds(metrics)
                    
                    # 限制历史记录长度
                    if len(self.metrics_history) > 1000:
                        self.metrics_history = self.metrics_history[-500:]
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.logger.log_error("性能监控循环出错", "PERFORMANCE", e)
                time.sleep(self.monitoring_interval)
    
    def _check_performance_thresholds(self, metrics: PerformanceMetrics):
        """检查性能阈值"""
        warnings = []
        
        if metrics.cpu_percent > self.performance_thresholds['cpu_percent']:
            warnings.append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.performance_thresholds['memory_percent']:
            warnings.append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
        
        for warning in warnings:
            console.print(f"[yellow]⚠️  {warning}[/yellow]")
            self.logger.log_warning(warning, "PERFORMANCE")
    
    def set_baseline(self):
        """设置基准性能"""
        self.baseline_metrics = self.get_current_metrics()
        if self.baseline_metrics:
            console.print("[blue]📏 基准性能已设置[/blue]")
            self.logger.log_info("基准性能已设置", "PERFORMANCE")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        if not self.metrics_history:
            return {}
        
        # 计算统计数据
        cpu_values = [m.cpu_percent for m in self.metrics_history]
        memory_values = [m.memory_mb for m in self.metrics_history]
        memory_percent_values = [m.memory_percent for m in self.metrics_history]
        
        summary = {
            'monitoring_duration': self.metrics_history[-1].timestamp - self.metrics_history[0].timestamp,
            'sample_count': len(self.metrics_history),
            'cpu_stats': {
                'avg': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values)
            },
            'memory_stats': {
                'avg_mb': sum(memory_values) / len(memory_values),
                'max_mb': max(memory_values),
                'min_mb': min(memory_values),
                'avg_percent': sum(memory_percent_values) / len(memory_percent_values),
                'max_percent': max(memory_percent_values)
            },
            'baseline_comparison': None
        }
        
        # 与基准比较
        if self.baseline_metrics:
            current = self.metrics_history[-1]
            summary['baseline_comparison'] = {
                'cpu_change': current.cpu_percent - self.baseline_metrics.cpu_percent,
                'memory_change_mb': current.memory_mb - self.baseline_metrics.memory_mb,
                'memory_change_percent': current.memory_percent - self.baseline_metrics.memory_percent
            }
        
        return summary
    
    def display_performance_report(self):
        """显示性能报告"""
        summary = self.get_performance_summary()
        
        if not summary:
            console.print("[yellow]没有性能数据可显示[/yellow]")
            return
        
        console.print("\n[bold blue]📊 性能监控报告[/bold blue]")
        console.print("="*50)
        
        # 基本信息
        console.print(f"[cyan]监控时长:[/cyan] {summary['monitoring_duration']:.1f} 秒")
        console.print(f"[cyan]采样次数:[/cyan] {summary['sample_count']}")
        
        # CPU统计
        cpu_stats = summary['cpu_stats']
        console.print(f"\n[bold]CPU使用率:[/bold]")
        console.print(f"  平均: {cpu_stats['avg']:.1f}%")
        console.print(f"  最大: {cpu_stats['max']:.1f}%")
        console.print(f"  最小: {cpu_stats['min']:.1f}%")
        
        # 内存统计
        memory_stats = summary['memory_stats']
        console.print(f"\n[bold]内存使用:[/bold]")
        console.print(f"  平均: {memory_stats['avg_mb']:.1f} MB ({memory_stats['avg_percent']:.1f}%)")
        console.print(f"  峰值: {memory_stats['max_mb']:.1f} MB ({memory_stats['max_percent']:.1f}%)")
        console.print(f"  最小: {memory_stats['min_mb']:.1f} MB")
        
        # 基准比较
        if summary['baseline_comparison']:
            baseline = summary['baseline_comparison']
            console.print(f"\n[bold]与基准比较:[/bold]")
            console.print(f"  CPU变化: {baseline['cpu_change']:+.1f}%")
            console.print(f"  内存变化: {baseline['memory_change_mb']:+.1f} MB ({baseline['memory_change_percent']:+.1f}%)")
        
        # 性能建议
        self._display_performance_recommendations(summary)
    
    def _display_performance_recommendations(self, summary: Dict[str, Any]):
        """显示性能建议"""
        recommendations = []
        
        cpu_avg = summary['cpu_stats']['avg']
        memory_max_percent = summary['memory_stats']['max_percent']
        
        if cpu_avg > 70:
            recommendations.append("CPU使用率较高，考虑优化算法或减少并发处理")
        
        if memory_max_percent > 80:
            recommendations.append("内存使用率较高，考虑增加内存或优化内存使用")
        
        if summary['baseline_comparison']:
            baseline = summary['baseline_comparison']
            if baseline['memory_change_mb'] > 100:
                recommendations.append("内存使用增长较大，检查是否存在内存泄漏")
        
        if recommendations:
            console.print(f"\n[bold yellow]💡 性能优化建议:[/bold yellow]")
            for i, rec in enumerate(recommendations, 1):
                console.print(f"  {i}. {rec}")
        else:
            console.print(f"\n[bold green]✅ 性能表现良好[/bold green]")

class BenchmarkRunner:
    """基准测试运行器"""
    
    def __init__(self):
        """初始化基准测试运行器"""
        self.logger = get_logger()
        self.monitor = PerformanceMonitor()
        
    def run_video_processing_benchmark(self, test_video: str, time_ranges: str, roi: str = None) -> Dict[str, Any]:
        """
        运行视频处理基准测试
        
        Args:
            test_video: 测试视频文件
            time_ranges: 时间范围
            roi: 感兴趣区域
            
        Returns:
            基准测试结果
        """
        console.print("[bold blue]🏃 开始视频处理基准测试[/bold blue]")
        
        # 启动性能监控
        self.monitor.start_monitoring()
        self.monitor.set_baseline()
        
        start_time = time.time()
        
        try:
            # 导入并运行视频处理
            from cached_video_processor import CachedVideoProcessor
            
            processor = CachedVideoProcessor()
            
            # 第一次处理（无缓存）
            console.print("[blue]第一次处理（建立缓存）...[/blue]")
            first_start = time.time()
            output_files1 = processor.process_video_with_cache(
                test_video, time_ranges, "benchmark_output1", roi
            )
            first_duration = time.time() - first_start
            
            # 第二次处理（使用缓存）
            console.print("[blue]第二次处理（使用缓存）...[/blue]")
            second_start = time.time()
            output_files2 = processor.process_video_with_cache(
                test_video, time_ranges, "benchmark_output2", roi
            )
            second_duration = time.time() - second_start
            
            total_duration = time.time() - start_time
            
            # 获取处理统计
            stats = processor.get_processing_stats()
            
            # 停止监控
            self.monitor.stop_monitoring()
            performance_summary = self.monitor.get_performance_summary()
            
            # 计算性能指标
            cache_efficiency = (first_duration - second_duration) / first_duration * 100 if first_duration > 0 else 0
            
            benchmark_results = {
                'total_duration': total_duration,
                'first_processing_time': first_duration,
                'second_processing_time': second_duration,
                'cache_efficiency_percent': cache_efficiency,
                'cache_hit_count': stats['cache_hit_count'],
                'processed_count': stats['processed_count'],
                'output_files_count': len(output_files1),
                'performance_summary': performance_summary
            }
            
            # 显示结果
            self._display_benchmark_results(benchmark_results)
            
            return benchmark_results
            
        except Exception as e:
            self.monitor.stop_monitoring()
            self.logger.log_error("基准测试失败", "BENCHMARK", e)
            console.print(f"[red]❌ 基准测试失败: {e}[/red]")
            return {}
    
    def _display_benchmark_results(self, results: Dict[str, Any]):
        """显示基准测试结果"""
        console.print("\n[bold green]🏆 基准测试结果[/bold green]")
        console.print("="*50)
        
        console.print(f"[cyan]总耗时:[/cyan] {results['total_duration']:.2f} 秒")
        console.print(f"[cyan]首次处理:[/cyan] {results['first_processing_time']:.2f} 秒")
        console.print(f"[cyan]缓存处理:[/cyan] {results['second_processing_time']:.2f} 秒")
        console.print(f"[cyan]缓存效率:[/cyan] {results['cache_efficiency_percent']:.1f}%")
        console.print(f"[cyan]缓存命中:[/cyan] {results['cache_hit_count']} 次")
        console.print(f"[cyan]实际处理:[/cyan] {results['processed_count']} 次")
        console.print(f"[cyan]输出文件:[/cyan] {results['output_files_count']} 个")
        
        # 性能评级
        if results['cache_efficiency_percent'] > 80:
            grade = "[bold green]A+ (优秀)[/bold green]"
        elif results['cache_efficiency_percent'] > 60:
            grade = "[bold blue]A (良好)[/bold blue]"
        elif results['cache_efficiency_percent'] > 40:
            grade = "[bold yellow]B (一般)[/bold yellow]"
        else:
            grade = "[bold red]C (需要优化)[/bold red]"
        
        console.print(f"\n[bold]性能评级:[/bold] {grade}")

# 全局性能监控器实例
_global_performance_monitor = None

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例"""
    global _global_performance_monitor
    if _global_performance_monitor is None:
        _global_performance_monitor = PerformanceMonitor()
    return _global_performance_monitor
