#!/usr/bin/env python3
"""
缓存管理模块测试脚本
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path
from cache_manager import CacheManager
from rich.console import Console

console = Console()

def create_test_video_file(file_path: str, content: str = "test video content") -> bool:
    """创建测试视频文件"""
    try:
        with open(file_path, 'w') as f:
            f.write(content)
        return True
    except Exception as e:
        console.print(f"[red]创建测试文件失败: {e}[/red]")
        return False

def test_cache_manager():
    """测试缓存管理器的所有功能"""
    
    console.print("[bold blue]开始测试缓存管理模块[/bold blue]")
    
    # 创建临时目录用于测试
    test_dir = Path(tempfile.mkdtemp(prefix="cache_test_"))
    cache_dir = test_dir / "cache"
    
    console.print(f"测试目录: {test_dir}")
    
    try:
        # 1. 初始化缓存管理器
        console.print("\n[bold]1. 测试缓存管理器初始化[/bold]")
        cm = CacheManager(cache_dir=str(cache_dir), max_cache_age_days=7)
        
        # 2. 测试缓存统计
        console.print("\n[bold]2. 测试缓存统计[/bold]")
        cm.display_cache_stats()
        
        # 3. 创建测试视频文件
        console.print("\n[bold]3. 创建测试视频文件[/bold]")
        test_video1 = test_dir / "test_video1.mp4"
        test_video2 = test_dir / "test_video2.mp4"
        processed_video1 = test_dir / "processed_video1.mp4"
        processed_video2 = test_dir / "processed_video2.mp4"
        
        create_test_video_file(str(test_video1), "test video 1 content")
        create_test_video_file(str(test_video2), "test video 2 content")
        create_test_video_file(str(processed_video1), "processed video 1 content")
        create_test_video_file(str(processed_video2), "processed video 2 content")
        
        console.print(f"✅ 测试文件创建完成")
        
        # 4. 测试视频段哈希计算
        console.print("\n[bold]4. 测试视频段哈希计算[/bold]")
        hash1 = cm.calculate_video_segment_hash(str(test_video1), "00:00:10", "00:00:20", "1920:1080:0:0")
        hash2 = cm.calculate_video_segment_hash(str(test_video1), "00:00:10", "00:00:20", "1920:1080:0:0")
        hash3 = cm.calculate_video_segment_hash(str(test_video1), "00:00:15", "00:00:25", "1920:1080:0:0")
        
        console.print(f"相同参数哈希1: {hash1}")
        console.print(f"相同参数哈希2: {hash2}")
        console.print(f"不同参数哈希3: {hash3}")
        
        if hash1 == hash2:
            console.print("[green]✅ 相同参数生成相同哈希[/green]")
        else:
            console.print("[red]❌ 相同参数生成不同哈希[/red]")
            
        if hash1 != hash3:
            console.print("[green]✅ 不同参数生成不同哈希[/green]")
        else:
            console.print("[red]❌ 不同参数生成相同哈希[/red]")
        
        # 5. 测试缓存未命中
        console.print("\n[bold]5. 测试缓存未命中[/bold]")
        cached_file = cm.get_cached_video_segment(str(test_video1), "00:00:10", "00:00:20", "1920:1080:0:0")
        if cached_file is None:
            console.print("[green]✅ 缓存未命中（预期结果）[/green]")
        else:
            console.print(f"[red]❌ 意外的缓存命中: {cached_file}[/red]")
        
        # 6. 测试缓存视频段
        console.print("\n[bold]6. 测试缓存视频段[/bold]")
        success = cm.cache_video_segment(
            str(test_video1), "00:00:10", "00:00:20", 
            str(processed_video1), "1920:1080:0:0"
        )
        
        if success:
            console.print("[green]✅ 视频段缓存成功[/green]")
        else:
            console.print("[red]❌ 视频段缓存失败[/red]")
        
        # 7. 测试缓存命中
        console.print("\n[bold]7. 测试缓存命中[/bold]")
        cached_file = cm.get_cached_video_segment(str(test_video1), "00:00:10", "00:00:20", "1920:1080:0:0")
        if cached_file:
            console.print(f"[green]✅ 缓存命中: {cached_file}[/green]")
            
            # 验证缓存文件存在
            if os.path.exists(cached_file):
                console.print("[green]✅ 缓存文件存在[/green]")
            else:
                console.print("[red]❌ 缓存文件不存在[/red]")
        else:
            console.print("[red]❌ 缓存未命中（应该命中）[/red]")
        
        # 8. 缓存更多视频段
        console.print("\n[bold]8. 缓存更多视频段[/bold]")
        cm.cache_video_segment(str(test_video1), "00:00:15", "00:00:25", str(processed_video1), "1920:1080:0:0")
        cm.cache_video_segment(str(test_video2), "00:00:05", "00:00:15", str(processed_video2), "1280:720:0:0")
        
        # 9. 显示缓存统计和列表
        console.print("\n[bold]9. 显示缓存统计和列表[/bold]")
        cm.display_cache_stats()
        cm.list_cache_entries()
        
        # 10. 测试缓存清理
        console.print("\n[bold]10. 测试缓存清理[/bold]")
        cleaned_count = cm.cleanup_expired_cache()
        console.print(f"清理了 {cleaned_count} 个过期缓存")
        
        # 11. 测试重复处理检测
        console.print("\n[bold]11. 测试重复处理检测[/bold]")
        console.print("模拟重复处理同一视频段...")
        
        # 第一次处理（应该缓存）
        cached_file = cm.get_cached_video_segment(str(test_video1), "00:00:30", "00:00:40", "1920:1080:0:0")
        if cached_file is None:
            console.print("[blue]首次处理，无缓存，需要进行视频裁剪[/blue]")
            # 模拟处理
            create_test_video_file(str(test_dir / "new_processed.mp4"), "new processed content")
            cm.cache_video_segment(str(test_video1), "00:00:30", "00:00:40", str(test_dir / "new_processed.mp4"), "1920:1080:0:0")
        else:
            console.print(f"[yellow]意外的缓存命中: {cached_file}[/yellow]")
        
        # 第二次处理（应该命中缓存）
        cached_file = cm.get_cached_video_segment(str(test_video1), "00:00:30", "00:00:40", "1920:1080:0:0")
        if cached_file:
            console.print(f"[green]✅ 重复处理检测成功，跳过裁剪，使用缓存: {os.path.basename(cached_file)}[/green]")
        else:
            console.print("[red]❌ 重复处理检测失败[/red]")
        
        console.print("\n[bold green]缓存管理模块测试完成[/bold green]")
        return True
        
    except Exception as e:
        console.print(f"\n[bold red]测试过程中发生错误: {e}[/bold red]")
        return False
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_dir)
            console.print(f"[blue]测试目录已清理: {test_dir}[/blue]")
        except Exception as e:
            console.print(f"[yellow]清理测试目录失败: {e}[/yellow]")

def test_cache_with_real_video():
    """使用真实视频文件测试缓存功能"""
    console.print("\n[bold blue]使用真实视频文件测试缓存功能[/bold blue]")
    
    # 使用提供的测试视频
    test_video = "/home/<USER>/data/dms/byd/sc3e_r/250715/2025-07-14 14-22-29_000000_000500_1920_1080_0_0.mp4"
    
    if not os.path.exists(test_video):
        console.print(f"[yellow]测试视频不存在，跳过真实视频测试: {test_video}[/yellow]")
        return True
    
    try:
        cm = CacheManager(cache_dir="test_cache", max_cache_age_days=1)
        
        # 测试不同的视频段
        test_segments = [
            ("00:00:00", "00:00:10", "1920:1080:0:0"),
            ("00:00:05", "00:00:15", "1920:1080:0:0"),
            ("00:00:00", "00:00:10", "1280:720:0:0"),  # 不同ROI
        ]
        
        for i, (start, end, roi) in enumerate(test_segments, 1):
            console.print(f"\n测试视频段 {i}: {start}-{end}, ROI: {roi}")
            
            # 检查缓存
            cached_file = cm.get_cached_video_segment(test_video, start, end, roi)
            if cached_file:
                console.print(f"[green]缓存命中: {cached_file}[/green]")
            else:
                console.print("[blue]缓存未命中，需要处理[/blue]")
                
                # 模拟处理（这里只是复制原文件作为示例）
                processed_file = f"processed_segment_{i}.mp4"
                shutil.copy2(test_video, processed_file)
                
                # 缓存结果
                cm.cache_video_segment(test_video, start, end, processed_file, roi)
                
                # 清理临时文件
                os.remove(processed_file)
        
        # 显示最终统计
        cm.display_cache_stats()
        
        return True
        
    except Exception as e:
        console.print(f"[red]真实视频测试失败: {e}[/red]")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        console.print("""
缓存管理模块测试脚本

用法:
    python test_cache_manager.py [选项]

选项:
    --help          显示帮助信息
    --real-video    使用真实视频文件测试
    --basic         只运行基本功能测试

功能测试:
    1. 缓存管理器初始化
    2. 视频段哈希计算
    3. 缓存存储和检索
    4. 缓存命中率测试
    5. 重复处理检测
    6. 缓存清理功能
    7. 缓存统计信息

注意:
    - 测试会创建临时文件和目录
    - 测试完成后会自动清理
    - 真实视频测试需要指定的视频文件存在
        """)
        return
    
    success = True
    
    if len(sys.argv) > 1 and sys.argv[1] == "--real-video":
        success = test_cache_with_real_video()
    elif len(sys.argv) > 1 and sys.argv[1] == "--basic":
        success = test_cache_manager()
    else:
        # 运行所有测试
        success = test_cache_manager()
        if success:
            success = test_cache_with_real_video()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
