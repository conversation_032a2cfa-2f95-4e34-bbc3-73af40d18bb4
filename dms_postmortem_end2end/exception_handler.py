#!/usr/bin/env python3
"""
异常处理和恢复管理器
提供完整的异常处理、错误恢复和优雅退出功能
"""

import os
import sys
import signal
import functools
import traceback
from typing import Callable, Any, Optional, Dict
from rich.console import Console
from system_logger import get_logger
from cleanup_manager import get_cleanup_manager

console = Console()

class ExceptionHandler:
    """异常处理和恢复管理器"""
    
    def __init__(self):
        """初始化异常处理器"""
        self.logger = get_logger()
        self.cleanup_manager = get_cleanup_manager()
        self.shutdown_handlers = []
        self.recovery_strategies = {}
        
        # 设置信号处理器
        self._setup_signal_handlers()
        
        console.print("[blue]🛡️  异常处理器初始化完成[/blue]")
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            signal_name = signal.Signals(signum).name
            console.print(f"\n[yellow]⚠️  接收到信号 {signal_name}，开始优雅退出...[/yellow]")
            self.logger.log_warning(f"接收到信号 {signal_name}", "SIGNAL")
            self.graceful_shutdown()
            sys.exit(0)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
        
        # 在Windows上不支持SIGHUP
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)
    
    def register_shutdown_handler(self, handler: Callable):
        """注册关闭处理器"""
        self.shutdown_handlers.append(handler)
        self.logger.log_info(f"注册关闭处理器: {handler.__name__}", "EXCEPTION")
    
    def register_recovery_strategy(self, exception_type: type, strategy: Callable):
        """注册恢复策略"""
        self.recovery_strategies[exception_type] = strategy
        self.logger.log_info(f"注册恢复策略: {exception_type.__name__}", "EXCEPTION")
    
    def graceful_shutdown(self):
        """优雅关闭"""
        console.print("[blue]🔄 执行优雅关闭流程...[/blue]")
        
        # 执行关闭处理器
        for handler in self.shutdown_handlers:
            try:
                console.print(f"[blue]执行关闭处理器: {handler.__name__}[/blue]")
                handler()
            except Exception as e:
                self.logger.log_error(f"关闭处理器执行失败: {handler.__name__}", "EXCEPTION", e)
        
        # 执行清理
        try:
            console.print("[blue]执行文件清理...[/blue]")
            self.cleanup_manager.cleanup_registered_files()
        except Exception as e:
            self.logger.log_error("清理文件失败", "EXCEPTION", e)
        
        # 结束日志会话
        try:
            self.logger.end_session()
        except Exception as e:
            console.print(f"[red]结束日志会话失败: {e}[/red]")
        
        console.print("[green]✅ 优雅关闭完成[/green]")
    
    def handle_exception(self, exception: Exception, context: str = "UNKNOWN") -> bool:
        """
        处理异常
        
        Args:
            exception: 异常对象
            context: 异常上下文
            
        Returns:
            bool: 是否成功恢复
        """
        exception_type = type(exception)
        
        # 记录异常
        self.logger.log_error(f"处理异常: {str(exception)}", context, exception)
        
        # 尝试恢复策略
        if exception_type in self.recovery_strategies:
            try:
                console.print(f"[yellow]🔧 尝试恢复策略: {exception_type.__name__}[/yellow]")
                recovery_func = self.recovery_strategies[exception_type]
                result = recovery_func(exception, context)
                
                if result:
                    console.print(f"[green]✅ 恢复成功[/green]")
                    self.logger.log_info(f"恢复成功: {exception_type.__name__}", context)
                    return True
                else:
                    console.print(f"[red]❌ 恢复失败[/red]")
                    self.logger.log_error(f"恢复失败: {exception_type.__name__}", context)
                    
            except Exception as recovery_error:
                console.print(f"[red]❌ 恢复策略执行失败: {recovery_error}[/red]")
                self.logger.log_error(f"恢复策略执行失败", context, recovery_error)
        
        return False
    
    def with_exception_handling(self, context: str = "FUNCTION", 
                               reraise: bool = False, 
                               default_return: Any = None):
        """
        异常处理装饰器
        
        Args:
            context: 上下文名称
            reraise: 是否重新抛出异常
            default_return: 默认返回值
        """
        def decorator(func: Callable):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    handled = self.handle_exception(e, f"{context}:{func.__name__}")
                    
                    if not handled and reraise:
                        raise
                    
                    return default_return
            return wrapper
        return decorator
    
    def safe_execute(self, func: Callable, *args, context: str = "SAFE_EXECUTE", 
                    default_return: Any = None, **kwargs) -> Any:
        """
        安全执行函数
        
        Args:
            func: 要执行的函数
            context: 上下文
            default_return: 默认返回值
            
        Returns:
            函数返回值或默认值
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.handle_exception(e, context)
            return default_return

# 预定义的恢复策略
def network_error_recovery(exception: Exception, context: str) -> bool:
    """网络错误恢复策略"""
    import time
    
    console.print("[yellow]🌐 网络错误，尝试重连...[/yellow]")
    
    # 等待一段时间后重试
    for attempt in range(3):
        console.print(f"[blue]重试第 {attempt + 1} 次...[/blue]")
        time.sleep(2 ** attempt)  # 指数退避
        
        # 这里可以添加具体的重连逻辑
        # 例如：重新建立SSH连接等
        
        # 模拟重连成功
        if attempt >= 1:  # 第2次尝试成功
            return True
    
    return False

def file_error_recovery(exception: Exception, context: str) -> bool:
    """文件错误恢复策略"""
    console.print("[yellow]📁 文件错误，尝试恢复...[/yellow]")
    
    # 检查磁盘空间
    import shutil
    try:
        free_space = shutil.disk_usage('.').free
        if free_space < 100 * 1024 * 1024:  # 小于100MB
            console.print("[red]磁盘空间不足，执行清理...[/red]")
            cleanup_manager = get_cleanup_manager()
            cleanup_manager.cleanup_all()
            return True
    except:
        pass
    
    # 检查权限
    if "Permission denied" in str(exception):
        console.print("[yellow]权限错误，尝试修改权限...[/yellow]")
        # 这里可以添加权限修复逻辑
        return False
    
    return False

def memory_error_recovery(exception: Exception, context: str) -> bool:
    """内存错误恢复策略"""
    console.print("[yellow]🧠 内存错误，尝试释放内存...[/yellow]")
    
    # 强制垃圾回收
    import gc
    gc.collect()
    
    # 清理缓存
    cleanup_manager = get_cleanup_manager()
    cleanup_manager.cleanup_registered_files()
    
    return True

# 全局异常处理器实例
_global_exception_handler = None

def get_exception_handler() -> ExceptionHandler:
    """获取全局异常处理器实例"""
    global _global_exception_handler
    if _global_exception_handler is None:
        _global_exception_handler = ExceptionHandler()
        
        # 注册默认恢复策略
        _global_exception_handler.register_recovery_strategy(ConnectionError, network_error_recovery)
        _global_exception_handler.register_recovery_strategy(OSError, file_error_recovery)
        _global_exception_handler.register_recovery_strategy(IOError, file_error_recovery)
        _global_exception_handler.register_recovery_strategy(MemoryError, memory_error_recovery)
        
    return _global_exception_handler

def with_exception_handling(context: str = "FUNCTION", reraise: bool = False, default_return: Any = None):
    """异常处理装饰器的便捷函数"""
    return get_exception_handler().with_exception_handling(context, reraise, default_return)

def safe_execute(func: Callable, *args, context: str = "SAFE_EXECUTE", 
                default_return: Any = None, **kwargs) -> Any:
    """安全执行函数的便捷函数"""
    return get_exception_handler().safe_execute(func, *args, context=context, 
                                               default_return=default_return, **kwargs)
