#!/usr/bin/env python3
"""
配置管理和用户交互模块
负责配置文件的读取、显示、验证和用户交互确认
"""

import os
import json
import time
import threading
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import signal
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.text import Text

console = Console()

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, cpp_kits_dir: str):
        """
        初始化配置管理器
        
        Args:
            cpp_kits_dir: cpp_kits目录路径
        """
        self.cpp_kits_dir = Path(cpp_kits_dir)
        self.ip_port_file = self.cpp_kits_dir / "ip_port.json"
        self.calidata_file = self.cpp_kits_dir / "calidata.json"
        self.configs = {}
        self.user_confirmed = False
        self.confirmation_timeout = False
        
    def load_configs(self) -> Dict[str, Any]:
        """加载所有配置文件"""
        configs = {}
        
        # 加载ip_port.json
        if self.ip_port_file.exists():
            try:
                with open(self.ip_port_file, 'r', encoding='utf-8') as f:
                    configs['ip_port'] = json.load(f)
            except Exception as e:
                console.print(f"[red]加载ip_port.json失败: {e}[/red]")
                configs['ip_port'] = None
        else:
            console.print(f"[yellow]ip_port.json不存在: {self.ip_port_file}[/yellow]")
            configs['ip_port'] = None
            
        # 加载calidata.json
        if self.calidata_file.exists():
            try:
                with open(self.calidata_file, 'r', encoding='utf-8') as f:
                    configs['calidata'] = json.load(f)
            except Exception as e:
                console.print(f"[red]加载calidata.json失败: {e}[/red]")
                configs['calidata'] = None
        else:
            console.print(f"[yellow]calidata.json不存在: {self.calidata_file}[/yellow]")
            configs['calidata'] = None
            
        self.configs = configs
        return configs
    
    def validate_ip_port_config(self, config: Dict[str, Any]) -> Tuple[bool, str]:
        """验证ip_port配置"""
        if not config:
            return False, "配置为空"
            
        # 检查必要字段
        required_fields = ['ip', 'port']
        for field in required_fields:
            if field not in config:
                return False, f"缺少必要字段: {field}"
                
        # 验证IP格式
        ip = config['ip']
        if not isinstance(ip, str):
            return False, "IP必须是字符串"
            
        # 简单的IP格式验证
        ip_parts = ip.split('.')
        if len(ip_parts) != 4:
            return False, "IP格式错误，应为x.x.x.x格式"
            
        try:
            for part in ip_parts:
                num = int(part)
                if not 0 <= num <= 255:
                    return False, f"IP段 {part} 超出范围 (0-255)"
        except ValueError:
            return False, "IP包含非数字字符"
            
        # 验证端口
        port = config['port']
        if not isinstance(port, int):
            return False, "端口必须是整数"
            
        if not 1 <= port <= 65535:
            return False, f"端口 {port} 超出范围 (1-65535)"
            
        return True, "验证通过"
    
    def validate_calidata_config(self, config: Dict[str, Any]) -> Tuple[bool, str]:
        """验证calidata配置"""
        if not config:
            return False, "配置为空"
            
        # 检查必要字段
        required_fields = [
            'head_yaw', 'head_pitch', 'head_roll',
            'left_eye_yaw', 'left_eye_pitch',
            'right_eye_yaw', 'right_eye_pitch',
            'left_eye_curve_mean', 'right_eye_curve_mean'
        ]
        
        for field in required_fields:
            if field not in config:
                return False, f"缺少必要字段: {field}"
                
        # 验证数值范围
        angle_fields = ['head_yaw', 'head_pitch', 'head_roll', 'left_eye_yaw', 'left_eye_pitch', 'right_eye_yaw', 'right_eye_pitch']
        curve_fields = ['left_eye_curve_mean', 'right_eye_curve_mean']
        
        for field in angle_fields:
            value = config[field]
            if not isinstance(value, (int, float)):
                return False, f"{field} 必须是数字"
            if not -180 <= value <= 180:
                return False, f"{field} 角度值 {value} 超出范围 (-180 到 180)"
                
        for field in curve_fields:
            value = config[field]
            if not isinstance(value, (int, float)):
                return False, f"{field} 必须是数字"
            if not 0 <= value <= 1:
                return False, f"{field} 曲率值 {value} 超出范围 (0 到 1)"
                
        return True, "验证通过"
    
    def display_configs(self) -> None:
        """显示配置文件内容"""
        console.print("\n" + "="*60)
        console.print("[bold blue]CPP程序配置文件内容[/bold blue]")
        console.print("="*60)
        
        # 显示ip_port.json
        if self.configs.get('ip_port'):
            console.print(f"\n[bold green]📁 ip_port.json[/bold green] ({self.ip_port_file})")
            
            # 验证配置
            is_valid, msg = self.validate_ip_port_config(self.configs['ip_port'])
            status_color = "green" if is_valid else "red"
            status_icon = "✅" if is_valid else "❌"
            console.print(f"[{status_color}]{status_icon} 验证状态: {msg}[/{status_color}]")
            
            # 创建表格显示
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("字段", style="cyan")
            table.add_column("值", style="yellow")
            table.add_column("说明", style="green")
            
            for key, value in self.configs['ip_port'].items():
                if key == 'ip':
                    desc = "远程服务器IP地址"
                elif key == 'port':
                    desc = "远程服务端口"
                else:
                    desc = "其他配置"
                table.add_row(key, str(value), desc)
                
            console.print(table)
        else:
            console.print(f"\n[bold red]❌ ip_port.json[/bold red] - 文件不存在或加载失败")
            
        # 显示calidata.json
        if self.configs.get('calidata'):
            console.print(f"\n[bold green]📁 calidata.json[/bold green] ({self.calidata_file})")
            
            # 验证配置
            is_valid, msg = self.validate_calidata_config(self.configs['calidata'])
            status_color = "green" if is_valid else "red"
            status_icon = "✅" if is_valid else "❌"
            console.print(f"[{status_color}]{status_icon} 验证状态: {msg}[/{status_color}]")
            
            # 创建表格显示
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("字段", style="cyan")
            table.add_column("值", style="yellow")
            table.add_column("说明", style="green")
            
            config_descriptions = {
                'head_yaw': '头部偏航角 (度)',
                'head_pitch': '头部俯仰角 (度)',
                'head_roll': '头部翻滚角 (度)',
                'left_eye_yaw': '左眼偏航角 (度)',
                'left_eye_pitch': '左眼俯仰角 (度)',
                'right_eye_yaw': '右眼偏航角 (度)',
                'right_eye_pitch': '右眼俯仰角 (度)',
                'left_eye_curve_mean': '左眼曲率均值',
                'right_eye_curve_mean': '右眼曲率均值'
            }
            
            for key, value in self.configs['calidata'].items():
                desc = config_descriptions.get(key, "未知配置")
                table.add_row(key, str(value), desc)
                
            console.print(table)
        else:
            console.print(f"\n[bold red]❌ calidata.json[/bold red] - 文件不存在或加载失败")
            
        console.print("\n" + "="*60)
    
    def timeout_handler(self, signum, frame):
        """超时处理器"""
        self.confirmation_timeout = True
        console.print(f"\n[yellow]⏰ 超时，自动确认配置[/yellow]")
        # 抛出异常来中断input()
        raise KeyboardInterrupt("Timeout")
        
    def wait_for_user_confirmation(self, timeout: int = 5) -> bool:
        """
        等待用户确认配置，支持超时自动确认

        Args:
            timeout: 超时时间（秒）

        Returns:
            bool: 用户是否确认
        """
        self.user_confirmed = False
        self.confirmation_timeout = False

        console.print(f"\n[bold yellow]⏳ 请确认以上配置是否正确 (将在 {timeout} 秒后自动确认)[/bold yellow]")
        console.print("[bold]选项:[/bold]")
        console.print("  [green]y/yes/回车[/green] - 确认配置正确，继续执行")
        console.print("  [red]n/no[/red] - 配置有误，进入修改模式")

        # 设置超时处理
        old_handler = signal.signal(signal.SIGALRM, self.timeout_handler)
        signal.alarm(timeout)

        try:
            user_input = input("\n请输入选择: ").strip().lower()
            signal.alarm(0)  # 取消超时

            if self.confirmation_timeout:
                console.print("[green]✅ 超时自动确认配置[/green]")
                return True  # 超时自动确认

            if user_input in ['', 'y', 'yes']:
                console.print("[green]✅ 用户确认配置正确[/green]")
                return True
            elif user_input in ['n', 'no']:
                console.print("[yellow]⚠️  用户选择修改配置[/yellow]")
                return False
            else:
                console.print("[yellow]输入无效，默认确认[/yellow]")
                return True

        except KeyboardInterrupt:
            signal.alarm(0)
            if self.confirmation_timeout:
                console.print("[green]✅ 超时自动确认配置[/green]")
                return True
            else:
                console.print("\n[red]用户中断操作[/red]")
                return False
        except Exception as e:
            signal.alarm(0)
            if self.confirmation_timeout:
                console.print("[green]✅ 超时自动确认配置[/green]")
                return True
            else:
                console.print(f"[red]输入处理出错: {e}，默认确认[/red]")
                return True
        finally:
            try:
                signal.signal(signal.SIGALRM, old_handler)
            except:
                pass
    
    def modify_config_interactive(self) -> bool:
        """交互式配置修改"""
        console.print("\n[bold blue]🔧 配置修改模式[/bold blue]")
        
        while True:
            console.print("\n请选择要修改的配置文件:")
            console.print("  [cyan]1[/cyan] - 修改 ip_port.json")
            console.print("  [cyan]2[/cyan] - 修改 calidata.json")
            console.print("  [cyan]3[/cyan] - 重新加载配置")
            console.print("  [cyan]4[/cyan] - 完成修改，返回")
            
            choice = Prompt.ask("请输入选择", choices=["1", "2", "3", "4"], default="4")
            
            if choice == "1":
                self._modify_ip_port_config()
            elif choice == "2":
                self._modify_calidata_config()
            elif choice == "3":
                console.print("[blue]重新加载配置...[/blue]")
                self.load_configs()
                self.display_configs()
            elif choice == "4":
                console.print("[green]配置修改完成[/green]")
                break
                
        return True
    
    def _modify_ip_port_config(self):
        """修改ip_port配置"""
        console.print("\n[bold]修改 ip_port.json[/bold]")
        
        current_config = self.configs.get('ip_port', {})
        new_config = {}
        
        # 修改IP
        current_ip = current_config.get('ip', '***********')
        new_ip = Prompt.ask(f"请输入IP地址", default=current_ip)
        new_config['ip'] = new_ip
        
        # 修改端口
        current_port = current_config.get('port', 1180)
        while True:
            try:
                new_port = int(Prompt.ask(f"请输入端口", default=str(current_port)))
                if 1 <= new_port <= 65535:
                    new_config['port'] = new_port
                    break
                else:
                    console.print("[red]端口必须在1-65535范围内[/red]")
            except ValueError:
                console.print("[red]请输入有效的端口号[/red]")
        
        # 验证新配置
        is_valid, msg = self.validate_ip_port_config(new_config)
        if is_valid:
            # 保存配置
            try:
                with open(self.ip_port_file, 'w', encoding='utf-8') as f:
                    json.dump(new_config, f, indent=2, ensure_ascii=False)
                console.print(f"[green]✅ ip_port.json 已保存[/green]")
                self.configs['ip_port'] = new_config
            except Exception as e:
                console.print(f"[red]保存失败: {e}[/red]")
        else:
            console.print(f"[red]配置验证失败: {msg}[/red]")
    
    def _modify_calidata_config(self):
        """修改calidata配置"""
        console.print("\n[bold]修改 calidata.json[/bold]")
        console.print("[yellow]注意: 角度值范围 -180 到 180，曲率值范围 0 到 1[/yellow]")
        
        current_config = self.configs.get('calidata', {})
        new_config = {}
        
        # 定义字段和默认值
        fields = {
            'head_yaw': ('头部偏航角', 0.0),
            'head_pitch': ('头部俯仰角', 0.0),
            'head_roll': ('头部翻滚角', 0.0),
            'left_eye_yaw': ('左眼偏航角', 0.0),
            'left_eye_pitch': ('左眼俯仰角', 0.0),
            'right_eye_yaw': ('右眼偏航角', 0.0),
            'right_eye_pitch': ('右眼俯仰角', 0.0),
            'left_eye_curve_mean': ('左眼曲率均值', 0.003),
            'right_eye_curve_mean': ('右眼曲率均值', 0.003)
        }
        
        for field, (desc, default_val) in fields.items():
            current_val = current_config.get(field, default_val)
            while True:
                try:
                    new_val = float(Prompt.ask(f"请输入{desc}", default=str(current_val)))
                    
                    # 验证范围
                    if 'curve' in field:
                        if 0 <= new_val <= 1:
                            new_config[field] = new_val
                            break
                        else:
                            console.print("[red]曲率值必须在0-1范围内[/red]")
                    else:
                        if -180 <= new_val <= 180:
                            new_config[field] = new_val
                            break
                        else:
                            console.print("[red]角度值必须在-180到180范围内[/red]")
                except ValueError:
                    console.print("[red]请输入有效的数字[/red]")
        
        # 验证新配置
        is_valid, msg = self.validate_calidata_config(new_config)
        if is_valid:
            # 保存配置
            try:
                with open(self.calidata_file, 'w', encoding='utf-8') as f:
                    json.dump(new_config, f, indent=2, ensure_ascii=False)
                console.print(f"[green]✅ calidata.json 已保存[/green]")
                self.configs['calidata'] = new_config
            except Exception as e:
                console.print(f"[red]保存失败: {e}[/red]")
        else:
            console.print(f"[red]配置验证失败: {msg}[/red]")
    
    def run_config_workflow(self, timeout: int = 5) -> bool:
        """
        运行完整的配置工作流程
        
        Args:
            timeout: 用户确认超时时间
            
        Returns:
            bool: 配置流程是否成功完成
        """
        try:
            # 1. 加载配置
            console.print("[blue]正在加载配置文件...[/blue]")
            self.load_configs()
            
            # 2. 显示配置
            self.display_configs()
            
            # 3. 等待用户确认
            while True:
                if self.wait_for_user_confirmation(timeout):
                    console.print("[green]✅ 配置确认完成，继续执行[/green]")
                    return True
                else:
                    # 用户选择修改配置
                    self.modify_config_interactive()
                    # 重新显示配置
                    self.display_configs()
                    
        except Exception as e:
            console.print(f"[red]配置工作流程出错: {e}[/red]")
            return False
