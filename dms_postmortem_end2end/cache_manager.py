#!/usr/bin/env python3
"""
智能缓存管理模块
基于文件内容哈希的缓存系统，避免重复处理相同视频段
"""

import os
import json
import hashlib
import time
import shutil
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from datetime import datetime, timedelta
from rich.console import Console

console = Console()

class CacheManager:
    """智能缓存管理器"""
    
    def __init__(self, cache_dir: str = ".cache", max_cache_age_days: int = 7):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录路径
            max_cache_age_days: 缓存最大保存天数
        """
        self.cache_dir = Path(cache_dir)
        self.max_cache_age_days = max_cache_age_days
        self.cache_index_file = self.cache_dir / "cache_index.json"
        self.cache_index = {}
        
        # 创建缓存目录
        self.cache_dir.mkdir(exist_ok=True)
        
        # 加载缓存索引
        self.load_cache_index()
        
        console.print(f"[blue]📦 缓存管理器初始化完成，缓存目录: {self.cache_dir}[/blue]")
    
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件的SHA256哈希值"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            console.print(f"[red]计算文件哈希失败 {file_path}: {e}[/red]")
            return None
    
    def calculate_video_segment_hash(self, video_path: str, start_time: str, end_time: str, roi: str = None) -> str:
        """
        计算视频段的唯一标识哈希
        
        Args:
            video_path: 视频文件路径
            start_time: 开始时间
            end_time: 结束时间
            roi: 感兴趣区域
            
        Returns:
            str: 视频段的唯一哈希值
        """
        try:
            # 获取视频文件的基本信息
            video_stat = os.stat(video_path)
            video_size = video_stat.st_size
            video_mtime = video_stat.st_mtime
            
            # 创建唯一标识字符串
            identifier_parts = [
                os.path.basename(video_path),
                str(video_size),
                str(video_mtime),
                start_time,
                end_time,
                roi or "no_roi"
            ]
            
            identifier_string = "|".join(identifier_parts)
            
            # 计算哈希
            hash_sha256 = hashlib.sha256()
            hash_sha256.update(identifier_string.encode('utf-8'))
            
            return hash_sha256.hexdigest()[:16]  # 使用前16位作为短哈希
            
        except Exception as e:
            console.print(f"[red]计算视频段哈希失败: {e}[/red]")
            return None
    
    def load_cache_index(self) -> None:
        """加载缓存索引"""
        try:
            if self.cache_index_file.exists():
                with open(self.cache_index_file, 'r', encoding='utf-8') as f:
                    self.cache_index = json.load(f)
                console.print(f"[green]✅ 缓存索引加载成功，共 {len(self.cache_index)} 个条目[/green]")
            else:
                self.cache_index = {}
                console.print("[blue]📝 创建新的缓存索引[/blue]")
        except Exception as e:
            console.print(f"[red]加载缓存索引失败: {e}[/red]")
            self.cache_index = {}
    
    def save_cache_index(self) -> bool:
        """保存缓存索引"""
        try:
            with open(self.cache_index_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_index, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            console.print(f"[red]保存缓存索引失败: {e}[/red]")
            return False
    
    def is_cache_valid(self, cache_entry: Dict[str, Any]) -> bool:
        """检查缓存条目是否有效"""
        try:
            # 检查缓存时间
            cache_time = datetime.fromisoformat(cache_entry['timestamp'])
            max_age = timedelta(days=self.max_cache_age_days)
            
            if datetime.now() - cache_time > max_age:
                return False
            
            # 检查缓存文件是否存在
            cache_file_path = self.cache_dir / cache_entry['cache_file']
            if not cache_file_path.exists():
                return False
            
            return True
            
        except Exception as e:
            console.print(f"[red]检查缓存有效性失败: {e}[/red]")
            return False
    
    def get_cached_video_segment(self, video_path: str, start_time: str, end_time: str, roi: str = None) -> Optional[str]:
        """
        获取缓存的视频段
        
        Args:
            video_path: 视频文件路径
            start_time: 开始时间
            end_time: 结束时间
            roi: 感兴趣区域
            
        Returns:
            Optional[str]: 缓存的视频文件路径，如果没有缓存则返回None
        """
        segment_hash = self.calculate_video_segment_hash(video_path, start_time, end_time, roi)
        if not segment_hash:
            return None
        
        if segment_hash in self.cache_index:
            cache_entry = self.cache_index[segment_hash]
            
            if self.is_cache_valid(cache_entry):
                cache_file_path = self.cache_dir / cache_entry['cache_file']
                console.print(f"[green]🎯 缓存命中: {cache_entry['description']}[/green]")
                console.print(f"[green]📁 缓存文件: {cache_file_path}[/green]")
                return str(cache_file_path)
            else:
                # 缓存过期或无效，删除条目
                console.print(f"[yellow]⏰ 缓存过期，删除条目: {cache_entry['description']}[/yellow]")
                self.remove_cache_entry(segment_hash)
        
        return None
    
    def cache_video_segment(self, video_path: str, start_time: str, end_time: str, 
                           processed_video_path: str, roi: str = None) -> bool:
        """
        缓存处理后的视频段
        
        Args:
            video_path: 原始视频文件路径
            start_time: 开始时间
            end_time: 结束时间
            processed_video_path: 处理后的视频文件路径
            roi: 感兴趣区域
            
        Returns:
            bool: 缓存是否成功
        """
        segment_hash = self.calculate_video_segment_hash(video_path, start_time, end_time, roi)
        if not segment_hash:
            return False
        
        try:
            # 生成缓存文件名
            cache_filename = f"video_segment_{segment_hash}.mp4"
            cache_file_path = self.cache_dir / cache_filename
            
            # 复制文件到缓存目录
            shutil.copy2(processed_video_path, cache_file_path)
            
            # 创建缓存条目
            cache_entry = {
                'original_video': os.path.basename(video_path),
                'start_time': start_time,
                'end_time': end_time,
                'roi': roi,
                'cache_file': cache_filename,
                'timestamp': datetime.now().isoformat(),
                'file_size': os.path.getsize(cache_file_path),
                'description': f"{os.path.basename(video_path)} [{start_time}-{end_time}]"
            }
            
            # 添加到索引
            self.cache_index[segment_hash] = cache_entry
            
            # 保存索引
            if self.save_cache_index():
                console.print(f"[green]💾 视频段已缓存: {cache_entry['description']}[/green]")
                console.print(f"[green]📁 缓存文件: {cache_file_path}[/green]")
                return True
            else:
                # 如果保存索引失败，删除缓存文件
                cache_file_path.unlink(missing_ok=True)
                return False
                
        except Exception as e:
            console.print(f"[red]缓存视频段失败: {e}[/red]")
            return False
    
    def remove_cache_entry(self, segment_hash: str) -> bool:
        """删除缓存条目"""
        try:
            if segment_hash in self.cache_index:
                cache_entry = self.cache_index[segment_hash]
                cache_file_path = self.cache_dir / cache_entry['cache_file']
                
                # 删除缓存文件
                cache_file_path.unlink(missing_ok=True)
                
                # 从索引中删除
                del self.cache_index[segment_hash]
                
                # 保存索引
                self.save_cache_index()
                
                console.print(f"[yellow]🗑️  已删除缓存: {cache_entry['description']}[/yellow]")
                return True
            
            return False
            
        except Exception as e:
            console.print(f"[red]删除缓存条目失败: {e}[/red]")
            return False
    
    def cleanup_expired_cache(self) -> int:
        """清理过期缓存"""
        console.print("[blue]🧹 开始清理过期缓存...[/blue]")
        
        expired_hashes = []
        
        for segment_hash, cache_entry in self.cache_index.items():
            if not self.is_cache_valid(cache_entry):
                expired_hashes.append(segment_hash)
        
        cleaned_count = 0
        for segment_hash in expired_hashes:
            if self.remove_cache_entry(segment_hash):
                cleaned_count += 1
        
        if cleaned_count > 0:
            console.print(f"[green]✅ 清理完成，删除了 {cleaned_count} 个过期缓存条目[/green]")
        else:
            console.print("[green]✅ 没有发现过期缓存[/green]")
        
        return cleaned_count
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            total_entries = len(self.cache_index)
            total_size = 0
            valid_entries = 0
            
            for cache_entry in self.cache_index.values():
                if self.is_cache_valid(cache_entry):
                    valid_entries += 1
                    total_size += cache_entry.get('file_size', 0)
            
            return {
                'total_entries': total_entries,
                'valid_entries': valid_entries,
                'expired_entries': total_entries - valid_entries,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'cache_dir': str(self.cache_dir),
                'max_age_days': self.max_cache_age_days
            }
            
        except Exception as e:
            console.print(f"[red]获取缓存统计失败: {e}[/red]")
            return {}
    
    def display_cache_stats(self) -> None:
        """显示缓存统计信息"""
        stats = self.get_cache_stats()
        
        if not stats:
            console.print("[red]无法获取缓存统计信息[/red]")
            return
        
        console.print("\n" + "="*50)
        console.print("[bold blue]📊 缓存统计信息[/bold blue]")
        console.print("="*50)
        
        console.print(f"[cyan]缓存目录:[/cyan] {stats['cache_dir']}")
        console.print(f"[cyan]最大保存天数:[/cyan] {stats['max_age_days']} 天")
        console.print(f"[cyan]总缓存条目:[/cyan] {stats['total_entries']}")
        console.print(f"[green]有效条目:[/green] {stats['valid_entries']}")
        console.print(f"[yellow]过期条目:[/yellow] {stats['expired_entries']}")
        console.print(f"[cyan]总缓存大小:[/cyan] {stats['total_size_mb']} MB")
        
        if stats['valid_entries'] > 0:
            console.print(f"\n[bold green]✅ 缓存系统正常运行[/bold green]")
        else:
            console.print(f"\n[bold yellow]⚠️  当前没有有效缓存[/bold yellow]")
        
        console.print("="*50)
    
    def list_cache_entries(self) -> None:
        """列出所有缓存条目"""
        console.print("\n[bold blue]📋 缓存条目列表[/bold blue]")
        
        if not self.cache_index:
            console.print("[yellow]没有缓存条目[/yellow]")
            return
        
        for i, (segment_hash, cache_entry) in enumerate(self.cache_index.items(), 1):
            is_valid = self.is_cache_valid(cache_entry)
            status_icon = "✅" if is_valid else "❌"
            status_color = "green" if is_valid else "red"
            
            console.print(f"\n[bold]{i}. {status_icon} {cache_entry['description']}[/bold]")
            console.print(f"   [cyan]哈希:[/cyan] {segment_hash}")
            console.print(f"   [cyan]时间段:[/cyan] {cache_entry['start_time']} - {cache_entry['end_time']}")
            console.print(f"   [cyan]ROI:[/cyan] {cache_entry.get('roi', 'None')}")
            console.print(f"   [cyan]缓存时间:[/cyan] {cache_entry['timestamp']}")
            console.print(f"   [cyan]文件大小:[/cyan] {cache_entry.get('file_size', 0) / 1024:.1f} KB")
            console.print(f"   [{status_color}]状态:[/{status_color}] {'有效' if is_valid else '过期/无效'}")
    
    def clear_all_cache(self) -> bool:
        """清空所有缓存"""
        try:
            console.print("[yellow]⚠️  正在清空所有缓存...[/yellow]")
            
            # 删除所有缓存文件
            for cache_entry in self.cache_index.values():
                cache_file_path = self.cache_dir / cache_entry['cache_file']
                cache_file_path.unlink(missing_ok=True)
            
            # 清空索引
            self.cache_index = {}
            
            # 保存空索引
            if self.save_cache_index():
                console.print("[green]✅ 所有缓存已清空[/green]")
                return True
            else:
                console.print("[red]❌ 清空缓存失败[/red]")
                return False
                
        except Exception as e:
            console.print(f"[red]清空缓存失败: {e}[/red]")
            return False
