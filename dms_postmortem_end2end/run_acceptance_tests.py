#!/usr/bin/env python3
"""
一键验收测试脚本
按阶段顺序运行所有验收测试，生成详细的验收报告
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

console = Console()

class AcceptanceTestRunner:
    """验收测试运行器"""
    
    def __init__(self):
        """初始化测试运行器"""
        self.test_results = {}
        self.start_time = time.time()
        
    def run_command(self, command: str, description: str, timeout: int = 60) -> bool:
        """
        运行命令并记录结果
        
        Args:
            command: 要执行的命令
            description: 测试描述
            timeout: 超时时间
            
        Returns:
            bool: 是否成功
        """
        console.print(f"\n[blue]🔄 {description}[/blue]")
        console.print(f"[cyan]命令: {command}[/cyan]")
        
        try:
            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=os.getcwd()
            )
            
            success = result.returncode == 0
            
            if success:
                console.print(f"[green]✅ {description} - 通过[/green]")
            else:
                console.print(f"[red]❌ {description} - 失败[/red]")
                console.print(f"[red]错误输出: {result.stderr[:200]}[/red]")
            
            self.test_results[description] = {
                'success': success,
                'command': command,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode
            }
            
            return success
            
        except subprocess.TimeoutExpired:
            console.print(f"[red]❌ {description} - 超时[/red]")
            self.test_results[description] = {
                'success': False,
                'command': command,
                'error': 'Timeout'
            }
            return False
        except Exception as e:
            console.print(f"[red]❌ {description} - 异常: {e}[/red]")
            self.test_results[description] = {
                'success': False,
                'command': command,
                'error': str(e)
            }
            return False
    
    def run_stage1_tests(self) -> bool:
        """运行阶段1验收测试"""
        console.print("\n" + "="*60)
        console.print("[bold blue]阶段1: 远程服务管理模块验收[/bold blue]")
        console.print("="*60)
        
        tests = [
            ("python test_remote_service.py", "远程服务管理功能测试")
        ]
        
        passed = 0
        for command, description in tests:
            if self.run_command(command, description):
                passed += 1
        
        success = passed == len(tests)
        console.print(f"\n[bold]阶段1结果: {passed}/{len(tests)} 通过[/bold]")
        return success
    
    def run_stage2_tests(self) -> bool:
        """运行阶段2验收测试"""
        console.print("\n" + "="*60)
        console.print("[bold blue]阶段2: 配置管理和用户交互模块验收[/bold blue]")
        console.print("="*60)
        
        tests = [
            ("python test_config_manager.py", "配置管理基本功能测试"),
            ("python test_config_manager.py --validation", "配置验证功能测试")
        ]
        
        passed = 0
        for command, description in tests:
            if self.run_command(command, description):
                passed += 1
        
        success = passed == len(tests)
        console.print(f"\n[bold]阶段2结果: {passed}/{len(tests)} 通过[/bold]")
        return success
    
    def run_stage3_tests(self) -> bool:
        """运行阶段3验收测试"""
        console.print("\n" + "="*60)
        console.print("[bold blue]阶段3: 智能缓存机制验收[/bold blue]")
        console.print("="*60)
        
        tests = [
            ("python test_cache_manager.py --basic", "基本缓存功能测试"),
            ("python test_cache_manager.py --real-video", "真实视频缓存测试")
        ]
        
        passed = 0
        for command, description in tests:
            if self.run_command(command, description):
                passed += 1
        
        success = passed == len(tests)
        console.print(f"\n[bold]阶段3结果: {passed}/{len(tests)} 通过[/bold]")
        return success
    
    def run_stage4_tests(self) -> bool:
        """运行阶段4验收测试"""
        console.print("\n" + "="*60)
        console.print("[bold blue]阶段4: 端到端自动化流程集成验收[/bold blue]")
        console.print("="*60)
        
        tests = [
            ("python test_workflow_simplified.py --test-type workflow", "端到端工作流程测试"),
            ("python test_workflow_simplified.py --test-type cache", "缓存效率验证测试")
        ]
        
        passed = 0
        for command, description in tests:
            if self.run_command(command, description):
                passed += 1
        
        success = passed == len(tests)
        console.print(f"\n[bold]阶段4结果: {passed}/{len(tests)} 通过[/bold]")
        return success
    
    def run_stage5_tests(self) -> bool:
        """运行阶段5验收测试"""
        console.print("\n" + "="*60)
        console.print("[bold blue]阶段5: 错误处理和系统优化验收[/bold blue]")
        console.print("="*60)
        
        tests = [
            ("python test_system_optimization.py --test all", "系统优化全面测试")
        ]
        
        passed = 0
        for command, description in tests:
            if self.run_command(command, description, timeout=120):  # 更长超时
                passed += 1
        
        success = passed == len(tests)
        console.print(f"\n[bold]阶段5结果: {passed}/{len(tests)} 通过[/bold]")
        return success
    
    def generate_report(self):
        """生成验收报告"""
        total_time = time.time() - self.start_time
        
        console.print("\n" + "="*80)
        console.print("[bold green]🎉 DMS端到端自动化流程验收报告[/bold green]")
        console.print("="*80)
        
        # 创建结果表格
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("阶段", style="cyan")
        table.add_column("测试项", style="yellow")
        table.add_column("状态", style="green")
        table.add_column("命令", style="blue")
        
        stage_mapping = {
            "远程服务管理功能测试": "阶段1",
            "配置管理基本功能测试": "阶段2",
            "配置验证功能测试": "阶段2",
            "基本缓存功能测试": "阶段3",
            "真实视频缓存测试": "阶段3",
            "端到端工作流程测试": "阶段4",
            "缓存效率验证测试": "阶段4",
            "系统优化全面测试": "阶段5"
        }
        
        passed_count = 0
        total_count = len(self.test_results)
        
        for test_name, result in self.test_results.items():
            stage = stage_mapping.get(test_name, "未知")
            status = "✅ 通过" if result['success'] else "❌ 失败"
            command = result['command']
            
            if result['success']:
                passed_count += 1
            
            table.add_row(stage, test_name, status, command)
        
        console.print(table)
        
        # 总结
        console.print(f"\n[bold]验收总结:[/bold]")
        console.print(f"[cyan]总测试数:[/cyan] {total_count}")
        console.print(f"[green]通过数:[/cyan] {passed_count}")
        console.print(f"[red]失败数:[/cyan] {total_count - passed_count}")
        console.print(f"[cyan]总耗时:[/cyan] {total_time:.1f} 秒")
        console.print(f"[cyan]通过率:[/cyan] {(passed_count/total_count)*100:.1f}%")
        
        if passed_count == total_count:
            console.print(f"\n[bold green]🎉 所有验收测试通过！DMS端到端自动化流程验收成功！[/bold green]")
            return True
        else:
            console.print(f"\n[bold red]❌ 验收测试未完全通过，请检查失败项目[/bold red]")
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有验收测试"""
        console.print("[bold cyan]🚀 DMS端到端自动化流程一键验收测试[/bold cyan]")
        console.print("[yellow]注意: 确保测试视频文件存在且远程服务器可访问[/yellow]")
        
        # 检查环境
        test_video = "/home/<USER>/data/dms/byd/sc3e_r/250715/2025-07-14 14-22-29_000000_000500_1920_1080_0_0.mp4"
        cpp_kits_dir = "BYD_HKH_R_2.01.07.2025.07.08.4_x86"
        
        if not os.path.exists(test_video):
            console.print(f"[red]❌ 测试视频不存在: {test_video}[/red]")
            return False
        
        if not os.path.exists(cpp_kits_dir):
            console.print(f"[red]❌ CPP Kits目录不存在: {cpp_kits_dir}[/red]")
            return False
        
        console.print(f"[green]✅ 环境检查通过[/green]")
        
        # 按阶段运行测试
        stage_results = []
        stage_results.append(self.run_stage1_tests())
        stage_results.append(self.run_stage2_tests())
        stage_results.append(self.run_stage3_tests())
        stage_results.append(self.run_stage4_tests())
        stage_results.append(self.run_stage5_tests())
        
        # 生成报告
        overall_success = self.generate_report()
        
        return overall_success

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='DMS端到端自动化流程验收测试')
    parser.add_argument('--stage', type=int, choices=[1, 2, 3, 4, 5], 
                       help='运行指定阶段的测试')
    parser.add_argument('--report-only', action='store_true',
                       help='只生成报告，不运行测试')
    
    args = parser.parse_args()
    
    runner = AcceptanceTestRunner()
    
    if args.report_only:
        console.print("[yellow]仅生成报告模式（需要先运行测试）[/yellow]")
        return 0
    
    if args.stage:
        console.print(f"[blue]运行阶段 {args.stage} 的验收测试[/blue]")
        if args.stage == 1:
            success = runner.run_stage1_tests()
        elif args.stage == 2:
            success = runner.run_stage2_tests()
        elif args.stage == 3:
            success = runner.run_stage3_tests()
        elif args.stage == 4:
            success = runner.run_stage4_tests()
        elif args.stage == 5:
            success = runner.run_stage5_tests()
        
        runner.generate_report()
    else:
        success = runner.run_all_tests()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
