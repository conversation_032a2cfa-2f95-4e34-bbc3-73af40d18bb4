# DMS视频处理端到端自动化流程 - 验收测试指南

## 概述
本文档提供了所有5个阶段的详细验收测试命令和步骤，确保功能对齐和可复现性。

---

## 阶段1: 远程服务管理模块验收

### 验收标准
1. ✅ 能够通过SSH连接到远程服务器
2. ✅ 能够校验远程模型文件与本地一致性
3. ✅ 能够检查远程服务状态并发现多实例问题
4. ✅ 能够清理重复服务实例，绝不重复启动
5. ✅ 所有远程操作包含完整错误处理和连接验证

### 验收命令
```bash
# 进入项目目录
cd /home/<USER>/tool_kit/dms_postmortem_end2end

# 运行远程服务管理测试
python test_remote_service.py

# 预期结果：
# - SSH连接成功
# - 配置文件显示正常
# - 模型文件一致性检查通过
# - 发现并清理重复服务实例
# - 最终只保留1个服务实例运行
```

### 验证文件
- `remote_service_manager.py` - 核心功能模块
- `test_remote_service.py` - 测试脚本
- `remote_config.json` - 远程服务配置

---

## 阶段2: 配置管理和用户交互模块验收

### 验收标准
1. ✅ 能够读取并显示ip_port.json和calidata.json内容
2. ✅ 实现5秒超时的用户确认机制
3. ✅ 提供配置修改界面和重新确认流程
4. ✅ 配置文件路径支持可配置化
5. ✅ 输入校验覆盖所有配置项

### 验收命令
```bash
# 基本功能测试
python test_config_manager.py

# 配置验证测试
python test_config_manager.py --validation

# 交互式工作流程演示（包含5秒超时）
python demo_config_workflow.py

# 预期结果：
# - 配置文件内容美观显示
# - 配置验证功能正常（IP格式、端口范围、角度范围等）
# - 5秒超时自动确认机制工作
# - 配置修改界面功能完整
```

### 验证文件
- `config_manager.py` - 核心功能模块
- `test_config_manager.py` - 测试脚本
- `demo_config_workflow.py` - 演示脚本

---

## 阶段3: 智能缓存机制验收

### 验收标准
1. ✅ 基于文件内容哈希的缓存系统正常工作
2. ✅ 能够检测并跳过重复视频段的裁剪步骤
3. ✅ 缓存索引文件能够正确维护
4. ✅ 缓存命中率测试通过（100%命中率，0秒处理时间）
5. ✅ 自动清理过期缓存机制工作正常

### 验收命令
```bash
# 基本缓存功能测试
python test_cache_manager.py --basic

# 真实视频缓存测试
python test_cache_manager.py --real-video

# 集成视频处理器测试（验证缓存效率）
python cached_video_processor.py "/home/<USER>/data/dms/byd/sc3e_r/250715/2025-07-14 14-22-29_000000_000500_1920_1080_0_0.mp4" "00:00:00-00:00:05;00:00:10-00:00:15" "test_output" "1920:1080:0:0"

# 再次运行相同命令验证缓存命中
python cached_video_processor.py "/home/<USER>/data/dms/byd/sc3e_r/250715/2025-07-14 14-22-29_000000_000500_1920_1080_0_0.mp4" "00:00:00-00:00:05;00:00:10-00:00:15" "test_output2" "1920:1080:0:0"

# 预期结果：
# - 第一次处理：建立缓存，有实际处理时间
# - 第二次处理：100%缓存命中，处理时间接近0秒
# - 缓存索引文件正确维护
# - 过期缓存自动清理
```

### 验证文件
- `cache_manager.py` - 核心功能模块
- `cached_video_processor.py` - 集成处理器
- `test_cache_manager.py` - 测试脚本

---

## 阶段4: 端到端自动化流程集成验收

### 验收标准
1. ✅ 主控制器脚本能够接收cpp_kits+长视频文件作为输入
2. ✅ 自动调用视频裁剪脚本处理视频段（带智能缓存）
3. ✅ 集成dms_postmortem_optimised.py调用接口
4. ✅ 完整的端到端自动化流程（前4步验证通过）
5. ✅ 全流程无需人工干预（配置5秒超时自动确认）

### 验收命令
```bash
# 简化端到端工作流程测试（推荐）
python test_workflow_simplified.py --test-type workflow

# 缓存效率验证测试
python test_workflow_simplified.py --test-type cache

# 完整端到端测试（包含所有步骤）
python test_workflow_simplified.py --test-type both

# 主控制器命令行接口测试
python dms_automation_controller.py --help

# 预期结果：
# - 管理器初始化成功
# - 远程服务管理正常
# - 配置管理自动确认
# - 视频处理带缓存正常
# - 缓存命中率100%
# - 全流程自动化无人工干预
```

### 验证文件
- `dms_automation_controller.py` - 主控制器
- `test_workflow_simplified.py` - 简化测试
- `test_end_to_end.py` - 完整测试

---

## 阶段5: 错误处理和系统优化验收

### 验收标准
1. ✅ 所有网络异常、文件异常、服务异常都有适当处理
2. ✅ 完整的日志记录系统工作正常
3. ✅ 中间文件自动清理机制正常
4. ✅ 系统在各种异常情况下都能优雅退出
5. ✅ 性能测试通过（缓存效率100%，处理时间优化）

### 验收命令
```bash
# 全面系统优化测试
python test_system_optimization.py --test all

# 单独测试各个组件
python test_system_optimization.py --test logging      # 日志系统
python test_system_optimization.py --test cleanup     # 清理系统
python test_system_optimization.py --test exception   # 异常处理
python test_system_optimization.py --test performance # 性能监控
python test_system_optimization.py --test benchmark   # 基准测试
python test_system_optimization.py --test integrated  # 集成测试

# 检查生成的日志文件
ls -la logs/
ls -la test_logs/

# 预期结果：
# - 6/6 测试全部通过
# - 日志记录系统正常（多级别日志、操作记录）
# - 文件清理系统正常（临时文件自动清理）
# - 异常处理系统正常（装饰器、安全执行）
# - 性能监控系统正常（实时监控、基准测试）
# - 所有组件无缝集成
```

### 验证文件
- `system_logger.py` - 日志记录系统
- `cleanup_manager.py` - 文件清理系统
- `exception_handler.py` - 异常处理系统
- `performance_monitor.py` - 性能监控系统
- `test_system_optimization.py` - 全面测试脚本

---

## 完整系统验收

### 最终验收命令
```bash
# 运行完整的端到端自动化流程（包含所有优化功能）
python test_workflow_simplified.py --test-type both

# 运行系统优化全面测试
python test_system_optimization.py --test all

# 检查项目计划表状态
cat project_plan.md

# 预期最终结果：
# - 所有5个阶段标记为"已完成"
# - 端到端工作流程正常运行
# - 缓存效率100%，性能优化显著
# - 错误处理和系统优化全面通过
# - 日志记录完整，文件清理正常
```

---

## 环境要求

### 必要文件
- 测试视频：`/home/<USER>/data/dms/byd/sc3e_r/250715/2025-07-14 14-22-29_000000_000500_1920_1080_0_0.mp4`
- CPP Kits目录：`BYD_HKH_R_2.01.07.2025.07.08.4_x86/`
- 远程服务器：*********** (SSH可访问)

### 依赖包
```bash
pip install paramiko rich psutil
```

### 验收通过标准
1. 所有测试脚本返回成功状态（exit code 0）
2. 控制台输出显示"✅ 测试通过"或"🎉 成功完成"
3. 没有"❌ 失败"或"ERROR"信息
4. 缓存命中率达到100%
5. 日志文件正常生成且内容完整

---

**My Lord，以上是所有5个阶段的详细验收指南，每个命令都可以独立运行验证对应功能。**
