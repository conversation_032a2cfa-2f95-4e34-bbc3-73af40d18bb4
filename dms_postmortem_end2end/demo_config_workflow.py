#!/usr/bin/env python3
"""
配置管理工作流程演示脚本
演示完整的配置显示、确认和修改流程
"""

import sys
from config_manager import ConfigManager
from rich.console import Console

console = Console()

def demo_config_workflow():
    """演示配置管理工作流程"""
    
    console.print("[bold blue]🚀 DMS配置管理工作流程演示[/bold blue]")
    console.print("="*60)
    
    # 使用实际的cpp_kits目录
    cpp_kits_dir = "BYD_HKH_R_2.01.07.2025.07.08.4_x86"
    
    try:
        cm = ConfigManager(cpp_kits_dir)
        
        console.print(f"[blue]📂 使用配置目录: {cpp_kits_dir}[/blue]")
        
        # 运行完整的配置工作流程
        # 这将：
        # 1. 加载配置文件
        # 2. 显示配置内容（带验证状态）
        # 3. 等待用户确认（5秒超时）
        # 4. 如果用户选择修改，提供交互式修改界面
        
        success = cm.run_config_workflow(timeout=5)
        
        if success:
            console.print("\n[bold green]🎉 配置工作流程完成！[/bold green]")
            console.print("[green]配置已确认，可以继续执行后续流程[/green]")
        else:
            console.print("\n[bold red]❌ 配置工作流程失败[/bold red]")
            console.print("[red]请检查配置文件或重新运行[/red]")
            
        return success
        
    except Exception as e:
        console.print(f"\n[bold red]演示过程中发生错误: {e}[/bold red]")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        console.print("""
配置管理工作流程演示

用法:
    python demo_config_workflow.py

功能演示:
    1. 自动加载 ip_port.json 和 calidata.json
    2. 显示配置内容和验证状态
    3. 等待用户确认（5秒超时自动确认）
    4. 支持交互式配置修改
    5. 完整的配置验证和保存

交互说明:
    - 直接回车或输入 y/yes：确认配置
    - 输入 n/no：进入配置修改模式
    - 等待5秒：自动确认配置
    - Ctrl+C：中断操作

配置修改功能:
    - 支持修改IP地址和端口
    - 支持修改所有校准参数
    - 实时验证输入值的有效性
    - 自动保存修改后的配置
        """)
        return
    
    console.print("[bold cyan]提示: 使用 --help 查看详细说明[/bold cyan]")
    console.print("[bold cyan]提示: 您可以在确认提示时输入 'n' 来测试配置修改功能[/bold cyan]\n")
    
    success = demo_config_workflow()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
