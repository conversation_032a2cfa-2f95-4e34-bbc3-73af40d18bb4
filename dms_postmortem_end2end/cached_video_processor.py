#!/usr/bin/env python3
"""
带缓存的视频处理器
集成缓存功能到视频裁剪流程中，避免重复处理相同视频段
"""

import os
import subprocess
import time
from typing import List, Tuple, Optional
from pathlib import Path
from cache_manager import CacheManager
from rich.console import Console

console = Console()

class CachedVideoProcessor:
    """带缓存的视频处理器"""
    
    def __init__(self, cache_dir: str = ".video_cache", max_cache_age_days: int = 7):
        """
        初始化带缓存的视频处理器
        
        Args:
            cache_dir: 缓存目录
            max_cache_age_days: 缓存最大保存天数
        """
        self.cache_manager = CacheManager(cache_dir, max_cache_age_days)
        self.processed_count = 0
        self.cache_hit_count = 0
        
        console.print(f"[blue]🎬 带缓存的视频处理器初始化完成[/blue]")
    
    def parse_time_ranges(self, time_ranges_str: str) -> List[Tuple[str, str]]:
        """
        解析时间范围字符串
        
        Args:
            time_ranges_str: 时间范围字符串，格式: "start1-end1;start2-end2"
            
        Returns:
            List[Tuple[str, str]]: 时间范围列表
        """
        try:
            ranges = []
            for range_str in time_ranges_str.split(';'):
                range_str = range_str.strip()
                if '-' in range_str:
                    start, end = range_str.split('-', 1)
                    ranges.append((start.strip(), end.strip()))
            return ranges
        except Exception as e:
            console.print(f"[red]解析时间范围失败: {e}[/red]")
            return []
    
    def build_ffmpeg_command(self, input_file: str, output_file: str, 
                           start_time: str, end_time: str, roi: Optional[str] = None) -> List[str]:
        """构建FFmpeg命令"""
        cmd = ['ffmpeg', '-y', '-i', input_file, '-ss', start_time, '-to', end_time]
        
        # 添加视频滤镜
        filters = []
        if roi:
            w, h, x, y = map(int, roi.split(':'))
            filters.append(f'crop={w}:{h}:{x}:{y}')
        
        if filters:
            cmd.extend(['-vf', ','.join(filters)])
        
        # 编码设置
        cmd.extend([
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-c:a', 'copy',
            output_file
        ])
        
        return cmd
    
    def process_video_segment(self, input_file: str, start_time: str, end_time: str, 
                            output_dir: str, roi: str = None) -> Optional[str]:
        """
        处理单个视频段（带缓存）
        
        Args:
            input_file: 输入视频文件
            start_time: 开始时间
            end_time: 结束时间
            output_dir: 输出目录
            roi: 感兴趣区域
            
        Returns:
            Optional[str]: 处理后的视频文件路径
        """
        try:
            # 检查缓存
            cached_file = self.cache_manager.get_cached_video_segment(
                input_file, start_time, end_time, roi
            )
            
            if cached_file:
                # 缓存命中，复制到输出目录
                self.cache_hit_count += 1

                # 确保输出目录存在
                os.makedirs(output_dir, exist_ok=True)

                # 生成输出文件名
                input_basename = Path(input_file).stem
                output_filename = f"{input_basename}_{start_time.replace(':', '')}-{end_time.replace(':', '')}.mp4"
                output_file = os.path.join(output_dir, output_filename)

                # 复制缓存文件到输出目录
                import shutil
                shutil.copy2(cached_file, output_file)

                console.print(f"[green]🎯 使用缓存，跳过处理: {output_filename}[/green]")
                return output_file
            
            # 缓存未命中，需要处理
            console.print(f"[blue]🔄 缓存未命中，开始处理: {start_time}-{end_time}[/blue]")
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成输出文件名
            input_basename = Path(input_file).stem
            output_filename = f"{input_basename}_{start_time.replace(':', '')}-{end_time.replace(':', '')}.mp4"
            output_file = os.path.join(output_dir, output_filename)
            
            # 构建FFmpeg命令
            cmd = self.build_ffmpeg_command(input_file, output_file, start_time, end_time, roi)
            
            # 执行FFmpeg
            start_process_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            process_time = time.time() - start_process_time
            
            if result.returncode == 0:
                console.print(f"[green]✅ 视频段处理完成: {output_filename} (耗时: {process_time:.1f}s)[/green]")
                
                # 缓存处理结果
                cache_success = self.cache_manager.cache_video_segment(
                    input_file, start_time, end_time, output_file, roi
                )
                
                if cache_success:
                    console.print(f"[green]💾 处理结果已缓存[/green]")
                else:
                    console.print(f"[yellow]⚠️  缓存保存失败[/yellow]")
                
                self.processed_count += 1
                return output_file
            else:
                console.print(f"[red]❌ 视频段处理失败: {result.stderr}[/red]")
                return None
                
        except subprocess.TimeoutExpired:
            console.print(f"[red]❌ 视频段处理超时: {start_time}-{end_time}[/red]")
            return None
        except Exception as e:
            console.print(f"[red]❌ 视频段处理出错: {e}[/red]")
            return None
    
    def process_video_with_cache(self, input_file: str, time_ranges: str, 
                               output_dir: str, roi: str = None) -> List[str]:
        """
        处理视频的多个时间段（带缓存）
        
        Args:
            input_file: 输入视频文件
            time_ranges: 时间范围字符串
            output_dir: 输出目录
            roi: 感兴趣区域
            
        Returns:
            List[str]: 处理后的视频文件路径列表
        """
        console.print(f"\n[bold blue]🎬 开始处理视频（带智能缓存）[/bold blue]")
        console.print(f"[cyan]输入文件:[/cyan] {input_file}")
        console.print(f"[cyan]时间范围:[/cyan] {time_ranges}")
        console.print(f"[cyan]输出目录:[/cyan] {output_dir}")
        console.print(f"[cyan]ROI:[/cyan] {roi or 'None'}")
        
        # 解析时间范围
        ranges = self.parse_time_ranges(time_ranges)
        if not ranges:
            console.print("[red]❌ 无效的时间范围[/red]")
            return []
        
        console.print(f"[blue]📋 共需处理 {len(ranges)} 个视频段[/blue]")
        
        # 清理过期缓存
        self.cache_manager.cleanup_expired_cache()
        
        # 处理每个视频段
        output_files = []
        start_time = time.time()
        
        for i, (start, end) in enumerate(ranges, 1):
            console.print(f"\n[bold]处理视频段 {i}/{len(ranges)}: {start} - {end}[/bold]")
            
            output_file = self.process_video_segment(
                input_file, start, end, output_dir, roi
            )
            
            if output_file:
                output_files.append(output_file)
            else:
                console.print(f"[red]❌ 视频段 {i} 处理失败[/red]")
        
        total_time = time.time() - start_time
        
        # 显示处理统计
        console.print(f"\n[bold green]🎉 视频处理完成[/bold green]")
        console.print(f"[cyan]总耗时:[/cyan] {total_time:.1f} 秒")
        console.print(f"[cyan]成功处理:[/cyan] {len(output_files)}/{len(ranges)} 个视频段")
        console.print(f"[cyan]缓存命中:[/cyan] {self.cache_hit_count} 次")
        console.print(f"[cyan]实际处理:[/cyan] {self.processed_count} 次")
        
        if self.cache_hit_count > 0:
            cache_hit_rate = (self.cache_hit_count / len(ranges)) * 100
            console.print(f"[green]📊 缓存命中率: {cache_hit_rate:.1f}%[/green]")
        
        # 显示缓存统计
        self.cache_manager.display_cache_stats()
        
        return output_files
    
    def get_processing_stats(self) -> dict:
        """获取处理统计信息"""
        return {
            'processed_count': self.processed_count,
            'cache_hit_count': self.cache_hit_count,
            'cache_stats': self.cache_manager.get_cache_stats()
        }
    
    def clear_cache(self) -> bool:
        """清空所有缓存"""
        return self.cache_manager.clear_all_cache()

def main():
    """主函数 - 演示用法"""
    import sys
    
    if len(sys.argv) < 4:
        console.print("""
带缓存的视频处理器

用法:
    python cached_video_processor.py <输入视频> <时间范围> <输出目录> [ROI]

参数:
    输入视频: 视频文件路径
    时间范围: 格式 "start1-end1;start2-end2"
    输出目录: 输出目录路径
    ROI: 可选，格式 "width:height:x:y"

示例:
    python cached_video_processor.py video.mp4 "00:00:10-00:00:20;00:01:00-00:01:10" output/ "1920:1080:0:0"
        """)
        return
    
    input_file = sys.argv[1]
    time_ranges = sys.argv[2]
    output_dir = sys.argv[3]
    roi = sys.argv[4] if len(sys.argv) > 4 else None
    
    if not os.path.exists(input_file):
        console.print(f"[red]❌ 输入文件不存在: {input_file}[/red]")
        return
    
    processor = CachedVideoProcessor()
    output_files = processor.process_video_with_cache(input_file, time_ranges, output_dir, roi)
    
    if output_files:
        console.print(f"\n[bold green]✅ 处理完成，生成了 {len(output_files)} 个文件[/bold green]")
        for i, file_path in enumerate(output_files, 1):
            console.print(f"  {i}. {file_path}")
    else:
        console.print(f"\n[bold red]❌ 处理失败[/bold red]")

if __name__ == "__main__":
    main()
