#!/usr/bin/env python3
"""
优化版视频切割工具
主要改进：
1. 解决终端显示问题 - 使用rich库替代原始ANSI转义序列
2. 内存和并发优化 - 流式处理FFmpeg输出，智能线程池管理
3. 输入验证和功能扩展 - 完善的参数校验，支持更多格式
4. 信号处理和优雅退出 - 确保终端状态正确恢复
"""

import os
import sys
import signal
import subprocess
import argparse
import json
import atexit
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock, Event
from pathlib import Path
import time
import psutil
from typing import List, Tuple, Optional, Dict, Any, Union

# Rich库用于更好的终端显示
from rich.console import Console
from rich.live import Live
from rich.table import Table
from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.panel import Panel
from rich.text import Text

# 全局变量用于优雅退出
shutdown_event = Event()
console = Console()

def safe_terminal_reset():
    """安全的终端重置函数"""
    try:
        import sys
        import os

        # 保存当前终端设置
        if hasattr(sys.stdout, 'isatty') and sys.stdout.isatty():
            # 发送完全重置序列
            reset_sequences = [
                '\033[!p',      # 软重置
                '\033[?25h',    # 显示光标
                '\033[0m',      # 重置所有属性
                '\033[?1049l',  # 退出备用屏幕缓冲区
                '\033[?1000l',  # 禁用鼠标跟踪
                '\033[?1002l',  # 禁用按钮事件跟踪
                '\033[?1003l',  # 禁用任何事件跟踪
                '\033[?1006l',  # 禁用SGR扩展鼠标模式
            ]

            for seq in reset_sequences:
                sys.stdout.write(seq)

            sys.stdout.flush()

            # 使用stty重置终端（如果可用）
            try:
                os.system('stty sane 2>/dev/null')
            except:
                pass

    except Exception:
        pass

class Config:
    """配置管理类"""
    DEFAULT_CONFIG = {
        'ffmpeg_preset': 'medium',
        'ffmpeg_crf': 23,
        'max_memory_usage': 80,  # 最大内存使用百分比
        'update_interval': 0.5,  # 进度更新间隔（秒）
        'supported_formats': ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv'],
        'output_format': 'mp4',
        'hardware_acceleration': 'auto'  # auto, none, nvenc, vaapi
    }
    
    def __init__(self, config_file: Optional[str] = None):
        self.config = self.DEFAULT_CONFIG.copy()
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
    
    def load_config(self, config_file: str):
        """从JSON文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                self.config.update(user_config)
        except Exception as e:
            console.print(f"[yellow]警告: 无法加载配置文件 {config_file}: {e}[/yellow]")
    
    def get(self, key: str, default=None):
        return self.config.get(key, default)

class InputValidator:
    """输入验证类"""
    
    @staticmethod
    def validate_video_file(file_path: str, config: Config) -> bool:
        """验证视频文件"""
        if not os.path.exists(file_path):
            console.print(f"[red]错误: 输入文件不存在: {file_path}[/red]")
            return False
        
        file_ext = Path(file_path).suffix.lower()
        if file_ext not in config.get('supported_formats'):
            console.print(f"[red]错误: 不支持的文件格式: {file_ext}[/red]")
            return False
        
        return True
    
    @staticmethod
    def validate_time_format(time_str: str) -> bool:
        """验证时间格式 HH:MM:SS"""
        try:
            parts = time_str.split(':')
            if len(parts) != 3:
                return False
            h, m, s = map(int, parts)
            return 0 <= h <= 23 and 0 <= m <= 59 and 0 <= s <= 59
        except ValueError:
            return False
    
    @staticmethod
    def validate_roi(roi_str: str, video_width: int, video_height: int) -> bool:
        """验证ROI参数"""
        try:
            w, h, x, y = map(int, roi_str.split(':'))
            return (w > 0 and h > 0 and x >= 0 and y >= 0 and 
                   x + w <= video_width and y + h <= video_height)
        except ValueError:
            return False

class ResourceMonitor:
    """资源监控类"""
    
    def __init__(self, max_memory_percent: int = 80):
        self.max_memory_percent = max_memory_percent
        self.process = psutil.Process()
    
    def check_memory_usage(self) -> bool:
        """检查内存使用是否超限"""
        memory_percent = psutil.virtual_memory().percent
        return memory_percent < self.max_memory_percent
    
    def get_optimal_thread_count(self) -> int:
        """根据系统资源计算最优线程数"""
        cpu_count = os.cpu_count()
        memory_gb = psutil.virtual_memory().total / (1024**3)
        
        # 对于视频处理（I/O密集型），使用更多线程
        # 但要考虑内存限制
        if memory_gb >= 16:
            return min(cpu_count * 2, 16)
        elif memory_gb >= 8:
            return min(cpu_count * 1.5, 8)
        else:
            return max(cpu_count, 4)

class ProgressDisplay:
    """进度显示类 - 使用Rich库避免终端显示问题"""
    
    def __init__(self, total_segments: int):
        self.total_segments = total_segments
        self.segments_status = {}
        self.lock = Lock()
        self.live = None
        self.progress = None
        self.task_ids = {}
        
    def start(self):
        """启动进度显示"""
        self.progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console
        )
        
        # 为每个片段创建进度任务
        for i in range(self.total_segments):
            task_id = self.progress.add_task(f"片段 {i+1}", total=100)
            self.task_ids[i] = task_id
        
        self.live = Live(self._create_display(), console=console, refresh_per_second=2)
        self.live.start()
    
    def stop(self):
        """停止进度显示并强制恢复终端状态"""
        if self.live:
            self.live.stop()
            # 强制恢复终端状态
            self._force_terminal_reset()

    def _force_terminal_reset(self):
        """强制重置终端状态"""
        try:
            # 使用全局的安全重置函数
            safe_terminal_reset()

            # 额外的console重置
            console.clear()
            console.show_cursor(True)
        except Exception:
            pass
    
    def update_segment(self, index: int, status: str, progress: float = 0, message: str = ""):
        """更新片段状态"""
        with self.lock:
            self.segments_status[index] = {
                'status': status,
                'progress': progress,
                'message': message
            }
            
            if self.progress and index in self.task_ids:
                task_id = self.task_ids[index]
                description = f"片段 {index+1}: {message}"
                self.progress.update(task_id, completed=progress, description=description)
    
    def _create_display(self) -> Panel:
        """创建显示面板"""
        if not self.progress:
            return Panel("初始化中...")
        
        return Panel(
            self.progress,
            title=f"视频片段处理进度 (共 {self.total_segments} 个片段)",
            border_style="blue"
        )

class FFmpegProcessor:
    """FFmpeg处理器 - 优化内存使用和错误处理"""
    
    def __init__(self, config: Config):
        self.config = config
    
    def get_video_info(self, input_file: str) -> Dict[str, Any]:
        """获取视频信息"""
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', input_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode != 0:
                raise RuntimeError(f"FFprobe失败: {result.stderr}")
            
            info = json.loads(result.stdout)
            video_stream = next(
                (s for s in info['streams'] if s['codec_type'] == 'video'), None
            )
            
            if not video_stream:
                raise RuntimeError("未找到视频流")
            
            return {
                'width': int(video_stream['width']),
                'height': int(video_stream['height']),
                'duration': float(info['format'].get('duration', 0)),
                'codec': video_stream.get('codec_name', 'unknown')
            }
        except subprocess.TimeoutExpired:
            raise RuntimeError("获取视频信息超时")
        except json.JSONDecodeError:
            raise RuntimeError("解析视频信息失败")
    
    def build_ffmpeg_command(self, input_file: str, output_file: str, 
                           start_time: str, end_time: str, roi: Optional[str] = None) -> List[str]:
        """构建FFmpeg命令"""
        cmd = ['ffmpeg', '-y', '-i', input_file, '-ss', start_time, '-to', end_time]
        
        # 添加视频滤镜
        filters = []
        if roi:
            w, h, x, y = map(int, roi.split(':'))
            filters.append(f'crop={w}:{h}:{x}:{y}')
        
        if filters:
            cmd.extend(['-vf', ','.join(filters)])
        
        # 编码设置
        preset = self.config.get('ffmpeg_preset', 'medium')
        crf = self.config.get('ffmpeg_crf', 23)
        
        cmd.extend([
            '-c:v', 'libx264',
            '-preset', preset,
            '-crf', str(crf),
            '-c:a', 'copy',  # 音频直接复制
            output_file
        ])
        
        return cmd

class VideoProcessor:
    """优化的视频处理器"""

    def __init__(self, input_file: str, output_dir: str, config: Config,
                 progress_display: ProgressDisplay, roi: Optional[str] = None):
        self.input_file = input_file
        self.output_dir = output_dir
        self.config = config
        self.progress_display = progress_display
        self.roi = roi
        self.ffmpeg_processor = FFmpegProcessor(config)
        self.video_info = self.ffmpeg_processor.get_video_info(input_file)

        # 验证ROI
        if roi and not InputValidator.validate_roi(roi, self.video_info['width'], self.video_info['height']):
            raise ValueError(f"无效的ROI参数: {roi}")

    def process_segment(self, start_time: str, end_time: str, index: int) -> Tuple[bool, str]:
        """处理单个视频片段"""
        if shutdown_event.is_set():
            return False, ""

        try:
            # 更新状态：开始处理
            self.progress_display.update_segment(index, 'processing', 0, f"{start_time} - {end_time}")

            # 生成输出文件名
            output_file = self._generate_output_filename(start_time, end_time)

            # 构建FFmpeg命令
            cmd = self.ffmpeg_processor.build_ffmpeg_command(
                self.input_file, output_file, start_time, end_time, self.roi
            )

            # 启动FFmpeg进程
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, bufsize=1, universal_newlines=True
            )

            # 流式处理输出，避免内存累积
            success = self._monitor_ffmpeg_process(process, start_time, end_time, index)

            if success:
                # 获取绝对路径
                abs_output_file = os.path.abspath(output_file)
                self.progress_display.update_segment(index, 'completed', 100, f"{start_time} - {end_time} 完成")
                return True, abs_output_file
            else:
                self.progress_display.update_segment(index, 'failed', 0, f"{start_time} - {end_time} 失败")
                return False, ""

        except Exception as e:
            self.progress_display.update_segment(index, 'failed', 0, f"异常: {str(e)[:30]}...")
            return False, ""

    def _generate_output_filename(self, start_time: str, end_time: str) -> str:
        """生成输出文件名"""
        base_name = Path(self.input_file).stem
        time_range = f"{start_time.replace(':', '')}_{end_time.replace(':', '')}"
        roi_part = f"_{self.roi.replace(':', '_')}" if self.roi else ""
        output_format = self.config.get('output_format', 'mp4')

        return os.path.join(self.output_dir, f"{base_name}_{time_range}{roi_part}.{output_format}")

    def _monitor_ffmpeg_process(self, process: subprocess.Popen,
                              start_time: str, end_time: str, index: int) -> bool:
        """监控FFmpeg进程并更新进度"""
        total_duration = self._time_to_seconds(end_time) - self._time_to_seconds(start_time)

        try:
            # 使用非阻塞方式读取stderr（FFmpeg的进度信息在stderr中）
            stderr_lines = []
            while True:
                if shutdown_event.is_set():
                    process.terminate()
                    return False

                # 检查进程是否结束
                if process.poll() is not None:
                    break

                # 读取stderr输出
                try:
                    line = process.stderr.readline()
                    if line:
                        stderr_lines.append(line)
                        # 解析进度信息
                        progress_percent = self._parse_ffmpeg_progress(line, total_duration)
                        if progress_percent is not None:
                            message = f"{start_time} - {end_time} ({progress_percent:.1f}%)"
                            self.progress_display.update_segment(index, 'processing', progress_percent, message)
                except:
                    pass

                time.sleep(0.1)  # 避免CPU占用过高

            # 等待进程完成
            return_code = process.wait(timeout=10)
            return return_code == 0

        except subprocess.TimeoutExpired:
            process.kill()
            return False
        except Exception:
            process.terminate()
            return False

    def _parse_ffmpeg_progress(self, line: str, total_duration: float) -> Optional[float]:
        """解析FFmpeg进度信息"""
        try:
            if 'time=' in line:
                # 提取时间信息，格式如: time=00:01:23.45
                time_part = line.split('time=')[1].split()[0]
                current_seconds = self._time_to_seconds(time_part)
                if total_duration > 0:
                    return min((current_seconds / total_duration) * 100, 100)
        except:
            pass
        return None

    def _time_to_seconds(self, time_str: str) -> float:
        """时间字符串转秒数"""
        try:
            parts = time_str.split(':')
            if len(parts) == 3:
                h, m, s = map(float, parts)
                return h * 3600 + m * 60 + s
        except:
            pass
        return 0.0

def signal_handler(signum, frame):
    """信号处理器 - 优雅退出"""
    console.print("\n[yellow]接收到中断信号，正在优雅退出...[/yellow]")
    shutdown_event.set()

def cleanup():
    """清理函数 - 确保终端状态恢复"""
    try:
        # 使用安全的终端重置
        safe_terminal_reset()

        # 使用console安全输出
        try:
            console.clear()
            console.show_cursor(True)
            console.print("\n[green]程序已安全退出，终端状态已恢复[/green]")
        except:
            # 如果console也有问题，使用原始print
            print("\n程序已安全退出，终端状态已恢复")

        # 最后再次确保终端状态
        safe_terminal_reset()

    except Exception:
        # 最后的保险措施
        try:
            print("\n程序已退出")
        except:
            pass

def parse_time_ranges(time_ranges_str: str) -> List[Tuple[str, str]]:
    """解析时间范围字符串"""
    ranges = []
    for range_str in time_ranges_str.split(';'):
        range_str = range_str.strip()
        if not range_str:
            continue

        if '-' not in range_str:
            raise ValueError(f"无效的时间范围格式: {range_str}")

        start, end = range_str.split('-', 1)
        start, end = start.strip(), end.strip()

        if not InputValidator.validate_time_format(start):
            raise ValueError(f"无效的开始时间格式: {start}")
        if not InputValidator.validate_time_format(end):
            raise ValueError(f"无效的结束时间格式: {end}")

        ranges.append((start, end))

    return ranges

def main():
    """主函数"""
    # 注册信号处理器和清理函数
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    atexit.register(cleanup)

    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description='优化版视频切割工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 cut_video_optimized.py -i input.mp4 -o output_dir -t "00:01:00-00:01:30;00:02:00-00:02:30"
  python3 cut_video_optimized.py -i input.mp4 -o output_dir -t "00:01:00-00:01:30" --roi 1280:800:0:0 --config config.json
        """
    )

    parser.add_argument('-i', '--input', required=True, help='输入视频文件')
    parser.add_argument('-o', '--output', required=True, help='输出目录')
    parser.add_argument('-t', '--time', required=True,
                       help='时间范围，格式: start1-end1;start2-end2')
    parser.add_argument('--roi', help='感兴趣区域，格式: width:height:x:y')
    parser.add_argument('--threads', type=int,
                       help='线程数（默认自动计算）')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--dry-run', action='store_true',
                       help='仅验证参数，不执行实际处理')

    args = parser.parse_args()

    try:
        # 加载配置
        config = Config(args.config)

        # 验证输入
        if not InputValidator.validate_video_file(args.input, config):
            return 1

        # 解析时间范围
        try:
            time_ranges = parse_time_ranges(args.time)
        except ValueError as e:
            console.print(f"[red]时间范围解析错误: {e}[/red]")
            return 1

        if not time_ranges:
            console.print("[red]错误: 未指定有效的时间范围[/red]")
            return 1

        # 创建输出目录
        os.makedirs(args.output, exist_ok=True)

        # 初始化资源监控
        resource_monitor = ResourceMonitor(config.get('max_memory_usage', 80))

        # 计算最优线程数
        if args.threads:
            thread_count = args.threads
        else:
            thread_count = resource_monitor.get_optimal_thread_count()

        console.print(f"[blue]使用 {thread_count} 个线程处理 {len(time_ranges)} 个片段[/blue]")

        # 如果是dry-run模式，只验证参数
        if args.dry_run:
            console.print("[green]参数验证通过，所有设置正确[/green]")
            return 0

        # 初始化进度显示
        progress_display = ProgressDisplay(len(time_ranges))

        # 初始化视频处理器
        try:
            processor = VideoProcessor(args.input, args.output, config, progress_display, args.roi)
        except Exception as e:
            console.print(f"[red]初始化视频处理器失败: {e}[/red]")
            return 1

        # 开始处理
        progress_display.start()

        try:
            with ThreadPoolExecutor(max_workers=thread_count) as executor:
                # 提交所有任务
                futures = [
                    executor.submit(processor.process_segment, start, end, i)
                    for i, (start, end) in enumerate(time_ranges)
                ]

                # 等待所有任务完成
                results = []
                output_files = []
                for future in as_completed(futures):
                    if shutdown_event.is_set():
                        break
                    try:
                        success, output_file = future.result(timeout=1)
                        results.append(success)
                        if success and output_file:
                            output_files.append(output_file)
                    except Exception as e:
                        console.print(f"[red]任务执行异常: {e}[/red]")
                        results.append(False)

        finally:
            progress_display.stop()
            # 确保终端状态完全恢复
            cleanup()

        # 显示最终结果
        if not shutdown_event.is_set():
            successful = sum(results)
            total = len(results)

            if successful == total:
                console.print(f"\n[green]✅ 处理完成！成功处理 {successful}/{total} 个片段[/green]")

                # 显示生成的文件路径
                if output_files:
                    console.print(f"\n[blue]📁 生成的视频文件路径：[/blue]")
                    for i, file_path in enumerate(output_files, 1):
                        console.print(f"  {i}. {file_path}")
                    console.print()

                return 0
            else:
                console.print(f"\n[yellow]⚠️  部分完成：成功 {successful}/{total} 个片段[/yellow]")

                # 显示成功生成的文件路径
                if output_files:
                    console.print(f"\n[blue]📁 成功生成的视频文件路径：[/blue]")
                    for i, file_path in enumerate(output_files, 1):
                        console.print(f"  {i}. {file_path}")
                    console.print()

                return 1
        else:
            console.print(f"\n[yellow]⚠️  用户中断，已处理 {len(results)} 个片段[/yellow]")

            # 显示已生成的文件路径
            if output_files:
                console.print(f"\n[blue]📁 已生成的视频文件路径：[/blue]")
                for i, file_path in enumerate(output_files, 1):
                    console.print(f"  {i}. {file_path}")
                console.print()

            return 130  # 标准的中断退出码

    except KeyboardInterrupt:
        console.print("\n[yellow]用户中断处理[/yellow]")
        return 130
    except Exception as e:
        console.print(f"\n[red]程序异常: {e}[/red]")
        return 1

if __name__ == '__main__':
    sys.exit(main())

# python3 cut_video_fromwhatyouwant_optimized.py -i /home/<USER>/data/dms/byd/hkh_r/250703/2025-07-04\ 10-01-15.mkv -o /home/<USER>/data/dms/byd/hkh_r/250703/ -t "00:04:57-00:05:17;00:15:10-00:15:30" --roi 1920:1080:0:0 --config video_config.json