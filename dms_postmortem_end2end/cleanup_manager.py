#!/usr/bin/env python3
"""
中间文件清理管理器
负责自动清理临时文件、缓存文件和中间处理文件
"""

import os
import shutil
import time
import tempfile
from typing import List, Set, Optional
from pathlib import Path
from rich.console import Console
from system_logger import get_logger

console = Console()

class CleanupManager:
    """中间文件清理管理器"""
    
    def __init__(self, auto_cleanup: bool = True):
        """
        初始化清理管理器
        
        Args:
            auto_cleanup: 是否自动清理
        """
        self.auto_cleanup = auto_cleanup
        self.temp_files: Set[str] = set()
        self.temp_dirs: Set[str] = set()
        self.cleanup_patterns: List[str] = [
            "*.tmp",
            "temp_*",
            "processed_segment_*.mp4",
            "video_segment_*.mp4"
        ]
        self.logger = get_logger()
        
        console.print(f"[blue]🧹 清理管理器初始化完成 (自动清理: {'开启' if auto_cleanup else '关闭'})[/blue]")
    
    def register_temp_file(self, file_path: str) -> str:
        """
        注册临时文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件路径
        """
        self.temp_files.add(str(Path(file_path).absolute()))
        self.logger.log_info(f"注册临时文件: {file_path}", "CLEANUP")
        return file_path
    
    def register_temp_dir(self, dir_path: str) -> str:
        """
        注册临时目录
        
        Args:
            dir_path: 目录路径
            
        Returns:
            str: 目录路径
        """
        self.temp_dirs.add(str(Path(dir_path).absolute()))
        self.logger.log_info(f"注册临时目录: {dir_path}", "CLEANUP")
        return dir_path
    
    def create_temp_file(self, suffix: str = "", prefix: str = "dms_temp_") -> str:
        """
        创建临时文件
        
        Args:
            suffix: 文件后缀
            prefix: 文件前缀
            
        Returns:
            str: 临时文件路径
        """
        fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
        os.close(fd)  # 关闭文件描述符
        
        self.register_temp_file(temp_path)
        return temp_path
    
    def create_temp_dir(self, prefix: str = "dms_temp_") -> str:
        """
        创建临时目录
        
        Args:
            prefix: 目录前缀
            
        Returns:
            str: 临时目录路径
        """
        temp_dir = tempfile.mkdtemp(prefix=prefix)
        self.register_temp_dir(temp_dir)
        return temp_dir
    
    def remove_file(self, file_path: str, force: bool = False) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            force: 是否强制删除
            
        Returns:
            bool: 是否成功删除
        """
        try:
            file_path = str(Path(file_path).absolute())
            
            if os.path.exists(file_path):
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    self.logger.log_info(f"删除文件: {file_path}", "CLEANUP")
                    
                    # 从注册列表中移除
                    self.temp_files.discard(file_path)
                    return True
                else:
                    self.logger.log_warning(f"路径不是文件: {file_path}", "CLEANUP")
                    return False
            else:
                self.logger.log_info(f"文件不存在，跳过删除: {file_path}", "CLEANUP")
                self.temp_files.discard(file_path)
                return True
                
        except Exception as e:
            self.logger.log_error(f"删除文件失败: {file_path}", "CLEANUP", e)
            if force:
                try:
                    # 强制删除
                    if os.path.exists(file_path):
                        os.chmod(file_path, 0o777)
                        os.remove(file_path)
                        self.logger.log_info(f"强制删除文件成功: {file_path}", "CLEANUP")
                        self.temp_files.discard(file_path)
                        return True
                except Exception as e2:
                    self.logger.log_error(f"强制删除文件失败: {file_path}", "CLEANUP", e2)
            return False
    
    def remove_dir(self, dir_path: str, force: bool = False) -> bool:
        """
        删除目录
        
        Args:
            dir_path: 目录路径
            force: 是否强制删除
            
        Returns:
            bool: 是否成功删除
        """
        try:
            dir_path = str(Path(dir_path).absolute())
            
            if os.path.exists(dir_path):
                if os.path.isdir(dir_path):
                    shutil.rmtree(dir_path)
                    self.logger.log_info(f"删除目录: {dir_path}", "CLEANUP")
                    
                    # 从注册列表中移除
                    self.temp_dirs.discard(dir_path)
                    return True
                else:
                    self.logger.log_warning(f"路径不是目录: {dir_path}", "CLEANUP")
                    return False
            else:
                self.logger.log_info(f"目录不存在，跳过删除: {dir_path}", "CLEANUP")
                self.temp_dirs.discard(dir_path)
                return True
                
        except Exception as e:
            self.logger.log_error(f"删除目录失败: {dir_path}", "CLEANUP", e)
            if force:
                try:
                    # 强制删除
                    if os.path.exists(dir_path):
                        # 修改权限后删除
                        for root, dirs, files in os.walk(dir_path):
                            for d in dirs:
                                os.chmod(os.path.join(root, d), 0o777)
                            for f in files:
                                os.chmod(os.path.join(root, f), 0o777)
                        shutil.rmtree(dir_path)
                        self.logger.log_info(f"强制删除目录成功: {dir_path}", "CLEANUP")
                        self.temp_dirs.discard(dir_path)
                        return True
                except Exception as e2:
                    self.logger.log_error(f"强制删除目录失败: {dir_path}", "CLEANUP", e2)
            return False
    
    def cleanup_by_pattern(self, base_dir: str, patterns: List[str] = None) -> int:
        """
        按模式清理文件
        
        Args:
            base_dir: 基础目录
            patterns: 文件模式列表
            
        Returns:
            int: 清理的文件数量
        """
        if patterns is None:
            patterns = self.cleanup_patterns
        
        cleaned_count = 0
        base_path = Path(base_dir)
        
        if not base_path.exists():
            return 0
        
        try:
            for pattern in patterns:
                for file_path in base_path.glob(pattern):
                    if file_path.is_file():
                        if self.remove_file(str(file_path)):
                            cleaned_count += 1
                    elif file_path.is_dir():
                        if self.remove_dir(str(file_path)):
                            cleaned_count += 1
            
            if cleaned_count > 0:
                self.logger.log_info(f"按模式清理完成: {base_dir}, 清理了 {cleaned_count} 个文件/目录", "CLEANUP")
            
        except Exception as e:
            self.logger.log_error(f"按模式清理失败: {base_dir}", "CLEANUP", e)
        
        return cleaned_count
    
    def cleanup_old_files(self, base_dir: str, max_age_hours: int = 24) -> int:
        """
        清理旧文件
        
        Args:
            base_dir: 基础目录
            max_age_hours: 最大文件年龄（小时）
            
        Returns:
            int: 清理的文件数量
        """
        cleaned_count = 0
        cutoff_time = time.time() - (max_age_hours * 3600)
        base_path = Path(base_dir)
        
        if not base_path.exists():
            return 0
        
        try:
            for file_path in base_path.rglob("*"):
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        if self.remove_file(str(file_path)):
                            cleaned_count += 1
            
            if cleaned_count > 0:
                self.logger.log_info(f"清理旧文件完成: {base_dir}, 清理了 {cleaned_count} 个文件", "CLEANUP")
            
        except Exception as e:
            self.logger.log_error(f"清理旧文件失败: {base_dir}", "CLEANUP", e)
        
        return cleaned_count
    
    def cleanup_registered_files(self) -> int:
        """
        清理所有注册的临时文件和目录
        
        Returns:
            int: 清理的文件/目录数量
        """
        cleaned_count = 0
        
        # 清理注册的临时文件
        for file_path in list(self.temp_files):
            if self.remove_file(file_path):
                cleaned_count += 1
        
        # 清理注册的临时目录
        for dir_path in list(self.temp_dirs):
            if self.remove_dir(dir_path):
                cleaned_count += 1
        
        if cleaned_count > 0:
            console.print(f"[green]🧹 清理了 {cleaned_count} 个注册的临时文件/目录[/green]")
        
        return cleaned_count
    
    def cleanup_all(self, base_dirs: List[str] = None) -> int:
        """
        执行全面清理
        
        Args:
            base_dirs: 要清理的基础目录列表
            
        Returns:
            int: 总清理数量
        """
        operation_index = self.logger.log_operation_start("全面清理", "CLEANUP")
        total_cleaned = 0
        
        try:
            # 1. 清理注册的临时文件
            console.print("[blue]清理注册的临时文件...[/blue]")
            total_cleaned += self.cleanup_registered_files()
            
            # 2. 按模式清理
            if base_dirs is None:
                base_dirs = [".", "output_video", "test_output", "test_output2", "test_output3"]
            
            for base_dir in base_dirs:
                if os.path.exists(base_dir):
                    console.print(f"[blue]按模式清理目录: {base_dir}[/blue]")
                    total_cleaned += self.cleanup_by_pattern(base_dir)
            
            # 3. 清理旧文件
            for base_dir in base_dirs:
                if os.path.exists(base_dir):
                    console.print(f"[blue]清理旧文件: {base_dir}[/blue]")
                    total_cleaned += self.cleanup_old_files(base_dir, max_age_hours=24)
            
            # 4. 清理缓存目录中的过期文件
            cache_dirs = [".video_cache", ".dms_cache", "test_cache"]
            for cache_dir in cache_dirs:
                if os.path.exists(cache_dir):
                    console.print(f"[blue]清理缓存目录: {cache_dir}[/blue]")
                    total_cleaned += self.cleanup_old_files(cache_dir, max_age_hours=168)  # 7天
            
            self.logger.log_operation_end(operation_index, "SUCCESS", cleaned_count=total_cleaned)
            console.print(f"[green]✅ 全面清理完成，共清理 {total_cleaned} 个文件/目录[/green]")
            
        except Exception as e:
            self.logger.log_operation_end(operation_index, "FAILED")
            self.logger.log_error("全面清理失败", "CLEANUP", e)
            console.print(f"[red]❌ 全面清理失败: {e}[/red]")
        
        return total_cleaned
    
    def get_cleanup_stats(self) -> dict:
        """获取清理统计信息"""
        return {
            'registered_temp_files': len(self.temp_files),
            'registered_temp_dirs': len(self.temp_dirs),
            'auto_cleanup_enabled': self.auto_cleanup,
            'cleanup_patterns': self.cleanup_patterns
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if self.auto_cleanup:
            self.cleanup_registered_files()

# 全局清理管理器实例
_global_cleanup_manager = None

def get_cleanup_manager() -> CleanupManager:
    """获取全局清理管理器实例"""
    global _global_cleanup_manager
    if _global_cleanup_manager is None:
        _global_cleanup_manager = CleanupManager()
    return _global_cleanup_manager
