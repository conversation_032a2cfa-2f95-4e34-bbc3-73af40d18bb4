#!/usr/bin/env python3
"""
系统优化和错误处理测试脚本
测试日志记录、异常处理、性能监控、文件清理等功能
"""

import os
import sys
import time
import tempfile
from rich.console import Console
from system_logger import init_logger, get_logger
from cleanup_manager import get_cleanup_manager
from exception_handler import get_exception_handler, with_exception_handling
from performance_monitor import get_performance_monitor, BenchmarkRunner

console = Console()

def test_logging_system():
    """测试日志记录系统"""
    console.print("[bold blue]📝 测试日志记录系统[/bold blue]")
    
    try:
        # 初始化日志记录器
        logger = init_logger(log_dir="test_logs", log_level="INFO")
        
        # 测试各种日志级别
        logger.log_info("这是一条信息日志", "TEST")
        logger.log_warning("这是一条警告日志", "TEST")
        logger.log_error("这是一条错误日志", "TEST", Exception("测试异常"))
        
        # 测试操作记录
        op_index = logger.log_operation_start("测试操作", "TEST", param1="value1")
        time.sleep(0.1)  # 模拟操作耗时
        logger.log_operation_end(op_index, "SUCCESS", result="测试成功")
        
        # 测试内存记录
        logger.log_memory_usage()
        
        console.print("[green]✅ 日志记录系统测试通过[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ 日志记录系统测试失败: {e}[/red]")
        return False

def test_cleanup_system():
    """测试文件清理系统"""
    console.print("\n[bold blue]🧹 测试文件清理系统[/bold blue]")
    
    try:
        cleanup_manager = get_cleanup_manager()
        
        # 创建临时文件和目录
        temp_file = cleanup_manager.create_temp_file(suffix=".test")
        temp_dir = cleanup_manager.create_temp_dir()
        
        console.print(f"创建临时文件: {temp_file}")
        console.print(f"创建临时目录: {temp_dir}")
        
        # 验证文件存在
        if not os.path.exists(temp_file):
            console.print("[red]❌ 临时文件创建失败[/red]")
            return False
        
        if not os.path.exists(temp_dir):
            console.print("[red]❌ 临时目录创建失败[/red]")
            return False
        
        # 测试清理
        stats_before = cleanup_manager.get_cleanup_stats()
        console.print(f"清理前统计: {stats_before}")
        
        cleaned_count = cleanup_manager.cleanup_registered_files()
        console.print(f"清理了 {cleaned_count} 个文件/目录")
        
        # 验证文件已删除
        if os.path.exists(temp_file):
            console.print("[red]❌ 临时文件清理失败[/red]")
            return False
        
        if os.path.exists(temp_dir):
            console.print("[red]❌ 临时目录清理失败[/red]")
            return False
        
        console.print("[green]✅ 文件清理系统测试通过[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ 文件清理系统测试失败: {e}[/red]")
        return False

def test_exception_handling():
    """测试异常处理系统"""
    console.print("\n[bold blue]🛡️  测试异常处理系统[/bold blue]")
    
    try:
        exception_handler = get_exception_handler()
        
        # 测试异常处理装饰器
        @with_exception_handling("TEST", reraise=False, default_return="默认值")
        def test_function_with_exception():
            raise ValueError("测试异常")
        
        @with_exception_handling("TEST", reraise=False, default_return="正常值")
        def test_function_normal():
            return "正常执行"
        
        # 测试异常情况
        result1 = test_function_with_exception()
        if result1 != "默认值":
            console.print(f"[red]❌ 异常处理装饰器测试失败: {result1}[/red]")
            return False
        
        # 测试正常情况
        result2 = test_function_normal()
        if result2 != "正常执行":
            console.print(f"[red]❌ 正常函数执行测试失败: {result2}[/red]")
            return False
        
        # 测试安全执行
        def risky_function():
            raise RuntimeError("危险操作")
        
        safe_result = exception_handler.safe_execute(
            risky_function, 
            context="SAFE_TEST", 
            default_return="安全默认值"
        )
        
        if safe_result != "安全默认值":
            console.print(f"[red]❌ 安全执行测试失败: {safe_result}[/red]")
            return False
        
        console.print("[green]✅ 异常处理系统测试通过[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ 异常处理系统测试失败: {e}[/red]")
        return False

def test_performance_monitoring():
    """测试性能监控系统"""
    console.print("\n[bold blue]📊 测试性能监控系统[/bold blue]")
    
    try:
        monitor = get_performance_monitor()
        
        # 启动监控
        monitor.start_monitoring()
        monitor.set_baseline()
        
        console.print("性能监控已启动，模拟工作负载...")
        
        # 模拟一些工作负载
        for i in range(5):
            # 模拟CPU密集型任务
            sum(range(100000))
            time.sleep(0.2)
        
        # 停止监控
        monitor.stop_monitoring()
        
        # 获取性能总结
        summary = monitor.get_performance_summary()
        
        if not summary:
            console.print("[red]❌ 性能监控数据为空[/red]")
            return False
        
        console.print(f"监控时长: {summary['monitoring_duration']:.1f} 秒")
        console.print(f"采样次数: {summary['sample_count']}")
        console.print(f"平均CPU: {summary['cpu_stats']['avg']:.1f}%")
        console.print(f"平均内存: {summary['memory_stats']['avg_mb']:.1f} MB")
        
        # 显示性能报告
        monitor.display_performance_report()
        
        console.print("[green]✅ 性能监控系统测试通过[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ 性能监控系统测试失败: {e}[/red]")
        return False

def test_benchmark_system():
    """测试基准测试系统"""
    console.print("\n[bold blue]🏃 测试基准测试系统[/bold blue]")
    
    test_video = "/home/<USER>/data/dms/byd/sc3e_r/250715/2025-07-14 14-22-29_000000_000500_1920_1080_0_0.mp4"
    
    if not os.path.exists(test_video):
        console.print(f"[yellow]⚠️  测试视频不存在，跳过基准测试: {test_video}[/yellow]")
        return True
    
    try:
        benchmark_runner = BenchmarkRunner()
        
        # 运行基准测试
        results = benchmark_runner.run_video_processing_benchmark(
            test_video,
            "00:00:00-00:00:02;00:00:03-00:00:05",
            "1280:800:0:0"
        )
        
        if not results:
            console.print("[red]❌ 基准测试失败[/red]")
            return False
        
        # 验证基准测试结果
        required_keys = ['total_duration', 'cache_efficiency_percent', 'output_files_count']
        for key in required_keys:
            if key not in results:
                console.print(f"[red]❌ 基准测试结果缺少字段: {key}[/red]")
                return False
        
        console.print("[green]✅ 基准测试系统测试通过[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ 基准测试系统测试失败: {e}[/red]")
        return False

def test_integrated_system():
    """测试集成系统"""
    console.print("\n[bold blue]🔧 测试集成系统[/bold blue]")
    
    try:
        # 测试增强版自动化控制器
        from dms_automation_controller import DMSAutomationController
        
        controller = DMSAutomationController("BYD_HKH_R_2.01.07.2025.07.08.4_x86")
        
        # 测试管理器初始化
        if not controller.initialize_managers():
            console.print("[red]❌ 管理器初始化失败[/red]")
            return False
        
        console.print("[green]✅ 集成系统测试通过[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ 集成系统测试失败: {e}[/red]")
        return False

def run_all_tests():
    """运行所有测试"""
    console.print("[bold cyan]🧪 系统优化和错误处理全面测试[/bold cyan]")
    console.print("="*60)
    
    tests = [
        ("日志记录系统", test_logging_system),
        ("文件清理系统", test_cleanup_system),
        ("异常处理系统", test_exception_handling),
        ("性能监控系统", test_performance_monitoring),
        ("基准测试系统", test_benchmark_system),
        ("集成系统", test_integrated_system),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        console.print(f"\n[bold]正在测试: {test_name}[/bold]")
        try:
            if test_func():
                passed += 1
            else:
                console.print(f"[red]❌ {test_name} 测试失败[/red]")
        except Exception as e:
            console.print(f"[red]❌ {test_name} 测试异常: {e}[/red]")
    
    # 显示测试总结
    console.print("\n" + "="*60)
    console.print(f"[bold]测试总结: {passed}/{total} 通过[/bold]")
    
    if passed == total:
        console.print("[bold green]🎉 所有测试通过！系统优化功能正常[/bold green]")
        return True
    else:
        console.print(f"[bold red]❌ {total - passed} 个测试失败[/bold red]")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='系统优化和错误处理测试')
    parser.add_argument('--test', choices=['logging', 'cleanup', 'exception', 'performance', 'benchmark', 'integrated', 'all'], 
                       default='all', help='测试类型')
    
    args = parser.parse_args()
    
    if args.test == 'all':
        success = run_all_tests()
    elif args.test == 'logging':
        success = test_logging_system()
    elif args.test == 'cleanup':
        success = test_cleanup_system()
    elif args.test == 'exception':
        success = test_exception_handling()
    elif args.test == 'performance':
        success = test_performance_monitoring()
    elif args.test == 'benchmark':
        success = test_benchmark_system()
    elif args.test == 'integrated':
        success = test_integrated_system()
    else:
        success = False
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
